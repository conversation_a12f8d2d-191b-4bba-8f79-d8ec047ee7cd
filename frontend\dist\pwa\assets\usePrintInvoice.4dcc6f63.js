import{bh as a,d as k,u as F,r as v,c as y,w as D,X as I,o as w,k as P,f as m,b as d,y as h,i as _,t as f,q as R,bw as C,h as b,a as V,p as X,l as U,aH as O,m as x,z as B,x as T,Q as L}from"./index.a0917d6f.js";import{_ as A}from"./plugin-vue_export-helper.21dcd24c.js";import{X as Q}from"./xero.1a0fcd7f.js";const q={fetch:({filter:e,pagination:r})=>a.get("/v1/orders",{params:{...e,...r}}),get:e=>a.get(`/v1/orders/${e}`),create:()=>a.post("/v1/orders"),updateNotes:(e,r)=>a.patch(`/v1/orders/${e}/notes`,{notes:r}),voidOrder:(e,r,s)=>a.post(`/v1/orders/${e}`,{reason:r,otherReason:s}),deleteOrder:e=>a.delete(`/v1/orders/${e}`),checkout:e=>a.post(`/v1/orders/${e.uuid}/checkout`,e),listWCHistory:({filter:e,pagination:r}={})=>a.get("/v1/wc-orders/history",{params:{...e,...r}}),wcFetchPending:()=>a.get("/v1/wc-orders/pending"),getWCOrder:e=>a.get(`/v1/wc-orders/${e}`),updateWCOrderStatus:(e,r)=>a.patch(`/v1/wc-orders/${e}/status`,{status:r}),updateWCOrderCustomerNote:(e,r)=>a.patch(`/v1/wc-orders/${e}/customer-note`,{customer_note:r}),syncWCOrderToXero:e=>a.post(`/v1/wc-orders/${e}/sync-xero`)};const W={class:"text-h6"},z={class:"text-subtitle2 text-grey-7 q-mt-sm"},N={key:0,class:"text-caption text-grey-6 q-mt-sm"},M=k({__name:"EmailInputDialog",props:{modelValue:{type:Boolean},defaultEmail:{default:""},customerEmail:{default:""},loading:{type:Boolean,default:!1},hintMessage:{default:""}},emits:["update:modelValue","confirm","cancel"],setup(e,{expose:r,emit:s}){F();const o=e,i=s,l=v(),t=v(""),c=y({get:()=>o.modelValue,set:n=>i("update:modelValue",n)}),E=y(()=>o.hintMessage&&o.hintMessage.length>0);D(()=>o.modelValue,n=>{n&&(t.value=o.customerEmail||o.defaultEmail||"",I(()=>{var p;(p=l.value)==null||p.focus()}))});const g=async()=>{var n;if(!t.value||!/.+@.+\..+/.test(t.value)){(n=l.value)==null||n.focus();return}i("confirm",t.value)},$=()=>{i("cancel"),c.value=!1};return r({resetForm:()=>{t.value=""}}),(n,p)=>(w(),P(T,{modelValue:c.value,"onUpdate:modelValue":p[1]||(p[1]=u=>c.value=u),persistent:""},{default:m(()=>[d(B,{style:{"min-width":"400px","max-width":"500px"}},{default:m(()=>[d(h,null,{default:m(()=>[_("div",W,f(n.$t("sendEmail")),1),_("div",z,f(n.$t("emailInput.confirmEmailAddress")),1)]),_:1}),d(h,{class:"q-pt-none"},{default:m(()=>[d(R(L),{modelValue:t.value,"onUpdate:modelValue":p[0]||(p[0]=u=>t.value=u),filled:"",type:"email",label:n.$t("emailInput.emailAddress"),rules:[u=>u&&u.length>0||n.$t("emailInput.emailRequired"),u=>/.+@.+\..+/.test(u)||n.$t("emailInput.invalidEmail")],"lazy-rules":"",ref_key:"emailInputRef",ref:l,onKeyup:C(g,["enter"])},{prepend:m(()=>[d(b,{name:"email"})]),_:1},8,["modelValue","label","rules"]),E.value?(w(),V("div",N,[d(b,{name:"info",size:"xs",class:"q-mr-xs"}),X(" "+f(n.hintMessage),1)])):U("",!0)]),_:1}),d(O,{align:"right"},{default:m(()=>[d(x,{flat:"",label:n.$t("cancel"),color:"grey",onClick:$,disable:n.loading},null,8,["label","disable"]),d(x,{label:n.$t("sendEmail"),color:"primary",onClick:g,loading:n.loading},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}});var G=A(M,[["__scopeId","data-v-1cff428c"]]);function J(){const e=async s=>{var o,i,l;if(!s.xero_sync||s.xero_sync.sync_status=="syncing"){const t=await q.get(s.uuid);if(s.xero_sync=t.result.xero_sync,!s.xero_sync)throw new Error("This order has not been synced to Xero, cannot print invoice")}if(((o=s.xero_sync)==null?void 0:o.xero_invoice_id)&&s.xero_sync.sync_status==="success")try{console.log(`Printing Xero invoice: ${s.xero_sync.xero_invoice_id}`),await r(s.xero_sync.xero_invoice_id);return}catch(t){console.error("Failed to print Xero invoice:",t);let c="Failed to print Xero invoice";throw t instanceof Error&&(t.message.includes("AUTHORISED")||t.message.includes("APPROVED")?c="This invoice needs to be authorised or approved in Xero before printing PDF":t.message.includes("popup blocked")?c="Browser popup blocked, please allow popups and try again":t.message.includes("too small")?c="Invalid PDF data received from Xero, please check invoice status":c=`Failed to print Xero invoice: ${t.message}`),new Error(c)}else throw((i=s.xero_sync)==null?void 0:i.sync_status)==="pending"?new Error("This order is still being synced to Xero, please try again later"):((l=s.xero_sync)==null?void 0:l.sync_status)==="failed"?new Error("Failed to sync this order to Xero, cannot print invoice"):new Error("This order has not been synced to Xero, cannot print invoice")},r=async s=>{try{console.log(`Requesting PDF for invoice: ${s}`);const o=await Q.getInvoicePDF(s);if(console.log("PDF Blob received:",o),console.log(`PDF Blob size: ${o.size} bytes`),console.log(`PDF Blob type: ${o.type}`),o.size<100)throw new Error("PDF data too small, likely invalid");o.type.includes("application/pdf")||console.warn(`Unexpected blob type: ${o.type}`);const i=URL.createObjectURL(o);console.log(`PDF URL created: ${i}`),console.log("Testing PDF URL before opening print window...");const l=window.open(i,"_blank");if(!l)throw URL.revokeObjectURL(i),new Error("Failed to open print window - popup blocked?");console.log("Print window opened successfully"),l.onload=()=>{console.log("PDF loaded in print window"),setTimeout(()=>{l.print()},1e3)},l.onerror=t=>{console.error("Error loading PDF in print window:",t),URL.revokeObjectURL(i),l.close()},setTimeout(()=>{URL.revokeObjectURL(i)},15e3)}catch(o){throw console.error("Error printing Xero invoice:",o),o}};return{printInvoice:e}}export{G as E,q as O,J as u};
