import{Q as Ve}from"./QSpace.b85838fc.js";import{u as re,r as k,o as v,k as b,f as l,b as e,z as G,y as $,i as I,t as o,bz as ke,q as r,Q as ie,l as S,aH as J,m as V,x as K,a$ as se,d as Ee,c as oe,s as we,w as Ie,a as L,h as D,p as u,F as H,j as Ce,v as De}from"./index.a0917d6f.js";import{Q as h,a as n}from"./QItemSection.e46fbd88.js";import{Q as R}from"./QItemLabel.900eb50d.js";import{Q as Se}from"./QList.709cd325.js";import{Q as Qe}from"./QScrollArea.05196630.js";import{u as qe}from"./use-quasar.ed0f225f.js";import{v as ne,c as Re}from"./order.ed4ce638.js";import{O as Y,E as $e,u as ze}from"./usePrintInvoice.4dcc6f63.js";import{X as j}from"./xero.1a0fcd7f.js";import{u as Ne}from"./useOrder.6a5cbe5a.js";import{f as Ue}from"./date.6d29930c.js";const Oe={class:"text-h6"},Le={__name:"VoidConfirmDialog",props:{orderId:{type:String,required:!0},otherReason:{type:String,default:""}},emits:["order-voided"],setup(M,{expose:B,emit:t}){const{t:E}=re(),Q=M,T=t,z=k(!1),y=k(""),C=k(""),q=()=>{z.value=!0,y.value="",C.value=Q.otherReason||"",Q.otherReason?y.value="other":y.value=ne[0].value},N=()=>{z.value=!1},a=()=>{if(!y.value){se.create({type:"warning",position:"top",message:E("orderVoided.reasonRequired")});return}if(y.value==="other"&&C.value===""){se.create({type:"warning",position:"top",message:E("orderVoided.otherReasonRequired")});return}const d={orderId:Q.orderId,reason:y.value,otherReason:y.value==="other"?C.value:null};T("order-voided",d),N()};return B({openDialog:q}),(d,w)=>(v(),b(K,{modelValue:z.value,"onUpdate:modelValue":w[2]||(w[2]=p=>z.value=p),persistent:"","no-refocus":""},{default:l(()=>[e(G,{style:{"min-width":"350px"}},{default:l(()=>[e($,null,{default:l(()=>[I("div",Oe,o(d.$t("orderVoided.title")),1)]),_:1}),e($,null,{default:l(()=>[e(ke,{modelValue:y.value,"onUpdate:modelValue":w[0]||(w[0]=p=>y.value=p),options:r(ne),type:"radio"},null,8,["modelValue","options"]),y.value==="other"?(v(),b(ie,{key:0,modelValue:C.value,"onUpdate:modelValue":w[1]||(w[1]=p=>C.value=p),modelModifiers:{trim:!0},type:"textarea",label:d.$t("reason"),class:"q-mt-md",outlined:"",rules:[p=>p.length>0||d.$t("orderVoided.otherReasonRequired")]},null,8,["modelValue","label","rules"])):S("",!0)]),_:1}),e(J,{align:"right"},{default:l(()=>[e(V,{flat:"",label:d.$t("cancel"),color:"primary",onClick:N},null,8,["label"]),e(V,{label:d.$t("void"),color:"negative",onClick:a},null,8,["label"])]),_:1})]),_:1})]),_:1},8,["modelValue"]))}},Xe={class:"row q-pt-sm"},Ae={class:"text-h5 text-bold"},Me={class:"row"},Be={class:"row"},Te={class:"row"},Fe={class:"row"},Pe={key:0,class:"text-caption text-grey"},He={key:0,class:"text-negative"},Ye={class:"row q-gutter-sm"},nl=Ee({__name:"OrderDetailDialog",props:{modelValue:{type:Boolean},orderID:{}},emits:["update:modelValue","refresh"],setup(M,{emit:B}){const{t}=re(),E=qe(),Q=Ne(),{printInvoice:T}=ze(),z=oe(()=>{var s,c,_,x,O;return!!(((c=(s=a.value)==null?void 0:s.xero_sync)==null?void 0:c.xero_invoice_id)&&((x=(_=a.value)==null?void 0:_.xero_sync)==null?void 0:x.sync_status)==="success"&&((O=a.value)==null?void 0:O.status)!=="void")}),y=k(),C=M,q=B,N=oe({get:()=>C.modelValue,set:s=>q("update:modelValue",s)}),a=k(),d=k(!1),w=k(!1),p=k(!1),F=k();we(()=>{U(),ue()}),Ie(()=>C.modelValue,s=>{a.value=void 0,s&&U()});const U=async()=>{const s=await Y.get(C.orderID);a.value=s.result},ue=async()=>{try{const s=await j.getConfig();F.value=s.result}catch(s){console.error("Failed to load Xero config:",s)}},de=()=>{N.value=!1},ce=()=>{p.value=!0},me=()=>{var _,x;if(!a.value)return"";const s=(_=a.value.customer)==null?void 0:_.email,c=(x=F.value)==null?void 0:x.default_email;return t(s?"emailInput.usingCustomerEmail":c?"emailInput.usingDefaultEmail":"emailInput.noDefaultEmail")},fe=async s=>{try{d.value=!0,await Y.voidOrder(s.orderId,s.reason,s.otherReason)}finally{d.value=!1,U(),q("refresh")}},X=k(!1),A=k(""),ve=()=>{var s;X.value=!0,A.value=((s=a.value)==null?void 0:s.notes)||""},P=()=>{X.value=!1},_e=async()=>{var s;if(!((s=a.value)!=null&&s.uuid)){P();return}try{d.value=!0,await Y.updateNotes(a.value.uuid,A.value)}finally{d.value=!1,U(),q("refresh"),P()}},pe=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return"cloud_off";switch(a.value.xero_sync.sync_status){case"success":return"cloud_done";case"failed":return"cloud_off";case"syncing":return"cloud_sync";case"pending":return"cloud_queue";default:return"cloud_off"}},ye=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return"grey";switch(a.value.xero_sync.sync_status){case"success":return"green";case"failed":return"red";case"syncing":return"blue";case"pending":return"orange";default:return"grey"}},ge=()=>{var s;if(!((s=a.value)!=null&&s.xero_sync))return t("xero.notSynced");switch(a.value.xero_sync.sync_status){case"success":return t("xero.syncSuccess");case"failed":return t("xero.syncFailed");case"syncing":return t("xero.syncing");case"pending":return t("xero.syncPending");default:return t("xero.notSynced")}},xe=async()=>{var s;if(!!((s=a.value)!=null&&s.uuid))try{w.value=!0;const c=await j.syncOrderToXero(a.value.uuid);E.notify({type:"positive",message:c.result.message||t("xero.syncSuccess"),position:"top"}),await U(),q("refresh")}catch(c){De(c)}finally{w.value=!1}},be=async()=>{if(!!a.value)try{d.value=!0,await T(a.value),E.notify({type:"positive",message:t("printInvoiceSuccess"),position:"top"})}catch(s){console.error("Print invoice error:",s),E.notify({type:"negative",message:t("printInvoiceError"),position:"top"})}finally{d.value=!1}},he=async s=>{var c;if(!!a.value){if(!((c=a.value.xero_sync)!=null&&c.xero_invoice_id)||a.value.xero_sync.sync_status!=="success"){E.notify({type:"warning",message:t("sendEmailNotSynced"),position:"top"}),p.value=!1;return}try{d.value=!0,await j.sendInvoiceEmail(a.value.xero_sync.xero_invoice_id,s),E.notify({type:"positive",message:t("sendEmailSuccess"),position:"top"}),p.value=!1}catch(_){console.error("Send email error:",_);let x=t("sendEmailError");if(_ instanceof Error)if(_.message.includes("daily email limit")||_.message.includes("Daily Email Rate Limit")){x=t("sendEmailRateLimitError"),E.dialog({title:t("sendEmailRateLimitTitle"),message:t("sendEmailRateLimitMessage"),ok:{label:t("understood"),color:"primary"},persistent:!1}),p.value=!1;return}else _.message.includes("invalid email")||_.message.includes("Invalid email")?x=t("sendEmailInvalidEmailError"):_.message.includes("manually from Xero")&&(x=t("sendEmailManualError"));E.notify({type:"negative",message:x,position:"top",timeout:5e3})}finally{d.value=!1}}};return(s,c)=>{var _,x,O,W;return v(),L(H,null,[e(K,{modelValue:N.value,"onUpdate:modelValue":c[1]||(c[1]=f=>N.value=f),class:"card-dialog","no-refocus":""},{default:l(()=>[e(G,{class:"column"},{default:l(()=>[e($,{class:"col-1 q-py-none"},{default:l(()=>[I("div",Xe,[I("div",Ae,o(r(t)("orderDetail")),1),e(Ve),e(V,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:c[0]||(c[0]=f=>q("update:modelValue",!1))})])]),_:1}),e($,{class:"col-10"},{default:l(()=>[e(Qe,{class:"full-height"},{default:l(()=>[e($,{class:"text-h6 q-mb-md q-pa-none"},{default:l(()=>{var f,m,Z,ee,le;return[I("div",Me,[e(h,{class:"col-12 col-md-6"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"event",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[u(o(r(t)("orderDate")),1)]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i;return[u(o(r(Ue)((i=a.value)==null?void 0:i.order_at,"YYYY-MM-DD HH:mm")),1)]}),_:1})]),_:1}),e(h,{class:"col-12 col-md-6"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"person",size:"sm"})]),_:1}),e(n,null,{default:l(()=>{var i,g;return[(g=(i=a.value)==null?void 0:i.customer)!=null&&g.name?(v(),b(R,{key:0},{default:l(()=>[u(o(a.value.customer.name),1)]),_:1})):(v(),b(R,{key:1,caption:""},{default:l(()=>[u(o(r(t)("unknown.customer")),1)]),_:1}))]}),_:1})]),_:1}),e(h,{class:"col-12 col-md-6"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"list",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[u(o(r(t)("itemNum")),1)]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i,g;return[u(o((g=(i=a.value)==null?void 0:i.order_items)==null?void 0:g.length),1)]}),_:1})]),_:1}),e(h,{class:"col-12 col-md-6"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"attach_money",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[u(o(r(t)("total")),1)]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i;return[u(" AU$ "+o((i=a.value)==null?void 0:i.total),1)]}),_:1})]),_:1}),e(h,{class:"col-12 col-md-6"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"check_circle",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[u(o(r(t)("status")),1)]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i;return[u(o(r(Re)(((i=a.value)==null?void 0:i.status)||"")),1)]}),_:1})]),_:1}),e(h,{class:"col-12"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"note",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[I("div",Be,[u(o(r(t)("note.label"))+" ",1),e(V,{flat:"",dense:"",onClick:ve,icon:"edit",size:"sm",class:"q-ml-xs",loading:d.value},null,8,["loading"])])]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i;return[u(o((i=a.value)==null?void 0:i.notes),1)]}),_:1})]),_:1}),((f=a.value)==null?void 0:f.status)=="void"?(v(),b(h,{key:0,class:"col-12"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"warning",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[I("div",Te,[u(o(r(t)("voidReason"))+" ",1),e(V,{flat:"",dense:"",onClick:y.value.openDialog,icon:"edit",size:"sm",class:"q-ml-xs",loading:d.value},null,8,["onClick","loading"])])]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i;return[u(o((i=a.value)==null?void 0:i.void_reason),1)]}),_:1})]),_:1})):S("",!0),((m=a.value)==null?void 0:m.status)=="completed"?(v(),b(h,{key:1,class:"col-12"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:pe(),size:"sm",color:ye()},null,8,["name","color"])]),_:1}),e(n,{side:""},{default:l(()=>{var i,g,ae,te;return[I("div",Fe,[u(o(r(t)("xero.syncStatus"))+" ",1),((g=(i=a.value)==null?void 0:i.xero_sync)==null?void 0:g.sync_status)!=="syncing"?(v(),b(V,{key:0,flat:"",dense:"",onClick:xe,icon:"sync",size:"sm",class:"q-ml-xs",loading:w.value,disable:((te=(ae=a.value)==null?void 0:ae.xero_sync)==null?void 0:te.sync_status)==="success"},null,8,["loading","disable"])):S("",!0)])]}),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i,g;return[I("div",null,[u(o(ge())+" ",1),(g=(i=a.value)==null?void 0:i.xero_sync)!=null&&g.xero_invoice_no?(v(),L("div",Pe," Invoice: "+o(a.value.xero_sync.xero_invoice_no),1)):S("",!0)])]}),_:1})]),_:1})):((Z=a.value)==null?void 0:Z.status)=="void"&&((le=(ee=a.value)==null?void 0:ee.xero_sync)==null?void 0:le.xero_invoice_no)?(v(),b(h,{key:2,class:"col-12"},{default:l(()=>[e(n,{side:""},{default:l(()=>[e(D,{name:"warning",size:"sm"})]),_:1}),e(n,{side:""},{default:l(()=>[u(o(r(t)("xero.voidedInvoice")),1)]),_:1}),e(n,{class:"text-subtitle1"},{default:l(()=>{var i,g;return[u(o((g=(i=a.value)==null?void 0:i.xero_sync)==null?void 0:g.xero_invoice_no),1)]}),_:1})]),_:1})):S("",!0)])]}),_:1}),e(Se,{class:"text-h6",bordered:"",separator:""},{default:l(()=>{var f;return[e(h,{class:"bg-grey-3"},{default:l(()=>[e(n,{class:"text-bold"},{default:l(()=>[u(o(r(t)("product.label")),1)]),_:1}),e(n,{class:"text-bold",side:""},{default:l(()=>[u(o(r(t)("price")),1)]),_:1})]),_:1}),(v(!0),L(H,null,Ce((f=a.value)==null?void 0:f.order_items,m=>(v(),b(h,{key:m.uuid},{default:l(()=>[e(n,null,{default:l(()=>[e(R,null,{default:l(()=>[u(o(m.product.name),1)]),_:2},1024),e(R,{class:"text-subtitle1"},{default:l(()=>[u(o(r(t)("quantity"))+": "+o(m.quantity)+" | ",1),m.is_free?(v(),L("span",He,o(r(t)("orderItem.is_free")),1)):(v(),L(H,{key:1},[u(o(r(t)("price"))+": AU$ "+o(m.price),1)],64))]),_:2},1024),m.rebate>0||m.discount>0?(v(),b(R,{key:0,class:"text-subtitle1"},{default:l(()=>[u(o(r(t)("discount"))+": - AU$ "+o(m.rebate+r(Q).getPercentageOff(r(Q).getItemSubtotal(m),m.rebate,m.discount)),1)]),_:2},1024)):S("",!0)]),_:2},1024),e(n,{class:"text-bold",side:""},{default:l(()=>[u(" AU$ "+o(r(Q).getItemTotal(m)),1)]),_:2},1024)]),_:2},1024))),128)),e(h,{class:"bg-grey-3"},{default:l(()=>[e(n,null,{default:l(()=>[e(R,{class:"text-bold"},{default:l(()=>[u(o(r(t)("total")),1)]),_:1})]),_:1}),e(n,{side:""},{default:l(()=>[e(R,{class:"text-bold"},{default:l(()=>{var m;return[u(" AU$ "+o((m=a.value)==null?void 0:m.total),1)]}),_:1})]),_:1})]),_:1})]}),_:1})]),_:1})]),_:1}),e(J,{align:"between",class:"col-1 bg-grey-2 q-pa-sm"},{default:l(()=>{var f,m;return[I("div",Ye,[((f=a.value)==null?void 0:f.status)!="void"?(v(),b(V,{key:0,color:"red",icon:"delete",label:r(t)("void"),onClick:y.value.openDialog,loading:d.value},null,8,["label","onClick","loading"])):S("",!0),((m=a.value)==null?void 0:m.status)=="completed"?(v(),b(V,{key:1,color:"primary",icon:"print",label:r(t)("printInvoice"),onClick:be,loading:d.value},null,8,["label","loading"])):S("",!0),z.value?(v(),b(V,{key:2,color:"secondary",icon:"email",label:r(t)("sendEmail"),onClick:ce,loading:d.value},null,8,["label","loading"])):S("",!0)]),e(V,{label:r(t)("close"),color:"grey",onClick:de},null,8,["label"])]}),_:1})]),_:1})]),_:1},8,["modelValue"]),e(Le,{ref_key:"orderVoidDialog",ref:y,"order-id":s.orderID,"other-reason":((_=a.value)==null?void 0:_.void_reason)||"",onOrderVoided:fe},null,8,["order-id","other-reason"]),e(K,{modelValue:X.value,"onUpdate:modelValue":c[3]||(c[3]=f=>X.value=f),persistent:""},{default:l(()=>[e(G,{style:{width:"500px","max-width":"100%"}},{default:l(()=>[e($,{class:"text-h6"},{default:l(()=>[u(o(r(t)("note.edit")),1)]),_:1}),e($,null,{default:l(()=>[I("p",null,o(r(t)("note.label")),1),e(ie,{modelValue:A.value,"onUpdate:modelValue":c[2]||(c[2]=f=>A.value=f),modelModifiers:{trim:!0},type:"textarea",rows:5,outlined:"",dense:""},null,8,["modelValue"])]),_:1}),e(J,{align:"right"},{default:l(()=>[e(V,{label:r(t)("cancel"),color:"negative",onClick:P,loading:d.value},null,8,["label","loading"]),e(V,{label:r(t)("save"),color:"positive",onClick:_e,loading:d.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1},8,["modelValue"]),e($e,{modelValue:p.value,"onUpdate:modelValue":c[4]||(c[4]=f=>p.value=f),"default-email":((x=F.value)==null?void 0:x.default_email)||"","customer-email":((W=(O=a.value)==null?void 0:O.customer)==null?void 0:W.email)||"",loading:d.value,"hint-message":me(),onConfirm:he,onCancel:c[5]||(c[5]=f=>p.value=!1)},null,8,["modelValue","default-email","customer-email","loading","hint-message"])],64)}}});export{nl as _};
