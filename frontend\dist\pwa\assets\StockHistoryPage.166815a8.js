import{d as k,u as h,r as u,c as q,s as w,n as Q,o as d,k as x,f as t,b as s,y as P,i as S,Q as V,e as B,h as y,g as C,a as p,t as f,z as I}from"./index.a0917d6f.js";import{Q as T}from"./QTd.0d1c5436.js";import{Q as N}from"./QTable.6af54a73.js";import{Q as U}from"./QPage.30a4ddee.js";import{I as D}from"./inventory.8905dd4e.js";import"./QList.709cd325.js";import"./QSelect.f8fcccb1.js";import"./QItemSection.e46fbd88.js";import"./QItemLabel.900eb50d.js";import"./QMenu.5058797d.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";import"./use-fullscreen.2c828d3b.js";const H={class:"row q-gutter-sm q-mb-sm"},M={key:0,class:"text-positive text-h6"},z={key:1,class:"text-warning text-h6"},A={key:2,class:"text-negative text-h6"},ee=k({__name:"StockHistoryPage",setup(E){const{t:n}=h(),g=u([]),_=q(()=>[{name:"name",label:n("product.name"),align:"left",field:"name",sortable:!0},{name:"barcode",label:n("barcode"),align:"left",field:"barcode",sortable:!0},{name:"category",label:n("category"),align:"left",field:"category",sortable:!0},{name:"stock_quantity",label:n("stockQuantity"),align:"left",field:"stock_qty",sortable:!0},{name:"unit",label:n("unit"),align:"left",field:"unit"}]),a=u({sortBy:"diff_stock_qty",descending:!1,page:1,rowsPerPage:20,rowsNumber:0}),b=l=>{if(!l)return;const o=l,{sortBy:c,descending:e}=o.pagination;c!=""&&(a.value.sortBy=c,a.value.descending=e),i()},r=u({search:""}),m=u(!1),i=async()=>{try{m.value=!0;const l=await D.listProductStocks({filter:r.value,pagination:a.value});g.value=l.result.data,a.value=l.result.pagination}finally{m.value=!1}},v=()=>{r.value.search="",i()};return w(()=>{i()}),(l,o)=>{const c=Q("TablePagination");return d(),x(U,null,{default:t(()=>[s(I,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:t(()=>[s(P,null,{default:t(()=>[s(N,{rows:g.value,columns:_.value,"row-key":"uuid","virtual-scroll":"",pagination:a.value,"onUpdate:pagination":o[2]||(o[2]=e=>a.value=e),"hide-pagination":"","binary-state-sort":"",class:"q-pa-sm full-height","table-header-class":"bg-grey-2",onRequest:b,loading:m.value},{top:t(()=>[S("div",H,[s(V,{filled:"",dense:"",modelValue:r.value.search,"onUpdate:modelValue":[o[0]||(o[0]=e=>r.value.search=e),i],debounce:300,label:"Search",class:"q-pb-md-sm"},B({prepend:t(()=>[s(y,{name:"search",class:"cursor-pointer"})]),_:2},[r.value.search?{name:"append",fn:t(()=>[s(y,{name:"close",class:"cursor-pointer",onClick:C(v,["stop"])})]),key:"0"}:void 0]),1032,["modelValue"])])]),bottom:t(()=>[s(c,{modelValue:a.value,"onUpdate:modelValue":o[1]||(o[1]=e=>a.value=e),onGetData:i},null,8,["modelValue"])]),"body-cell-stock_quantity":t(e=>[s(T,{props:e},{default:t(()=>[e.row.diff_stock_qty>5?(d(),p("span",M,f(e.row.stock_qty),1)):e.row.diff_stock_qty>0?(d(),p("span",z,f(e.row.stock_qty),1)):(d(),p("span",A,f(e.row.stock_qty),1))]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])]),_:1})]),_:1})]),_:1})}}});export{ee as default};
