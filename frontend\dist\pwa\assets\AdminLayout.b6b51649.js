import{d as k,aF as S,o as n,k as s,f as a,b as o,h as F,l as V,p as w,t as b,B as N,aG as x,a as d,j as h,F as c,q as g,bB as v,u as T,r as P,c as D,n as Q,m as f,i as M,A as O}from"./index.a0917d6f.js";import{Q as U,a as z}from"./QToolbar.56f560c5.js";import{Q as E}from"./QHeader.fff4b8ce.js";import{Q as H}from"./QDrawer.4d13cda9.js";import{Q as $,a as j}from"./QLayout.ecd06863.js";import{Q as G}from"./QExpansionItem.d19198c0.js";import{Q as R}from"./QList.709cd325.js";import{a as C,Q as X}from"./QItemSection.e46fbd88.js";import{Q as J}from"./QItemLabel.900eb50d.js";import{_ as K}from"./plugin-vue_export-helper.21dcd24c.js";import{u as W}from"./pageInfo.9ba74675.js";import{u as Y}from"./dialog.bc7c4a9f.js";import"./QScrollObserver.9d096e23.js";import"./TouchPan.7726afc4.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";const Z=k({__name:"MenuItem",props:{title:{},link:{},onClick:{type:Function},icon:{},isSubmenu:{type:Boolean},requireAdmin:{type:Boolean}},setup(p){const l=S(),e=p;function i(){var t;e.link?l.push(e.link):(t=e.onClick)==null||t.call(e)}return(t,u)=>(n(),s(X,{clickable:"",onClick:i,class:N({"bg-grey-2":t.isSubmenu})},{default:a(()=>[t.icon?(n(),s(C,{key:0,avatar:""},{default:a(()=>[o(F,{name:t.icon},null,8,["name"])]),_:1})):V("",!0),o(C,null,{default:a(()=>[o(J,null,{default:a(()=>[w(b(t.title),1)]),_:1})]),_:1})]),_:1},8,["class"]))}});var I=K(Z,[["__scopeId","data-v-287c4abf"]]);const ee=k({name:"MenuLink",__name:"Menu",props:{modelValue:{}},setup(p){const l=x();return(e,i)=>(n(),s(R,{bordered:"",separator:""},{default:a(()=>[(n(!0),d(c,null,h(e.modelValue,t=>(n(),d(c,{key:t.title},[!t.requireAdmin||g(l).isAdmin()?(n(),d(c,{key:0},[t.subMenus&&t.subMenus.length>0?(n(),s(G,{key:0,label:t.title,icon:t.icon,"expand-separator":""},{default:a(()=>[(n(!0),d(c,null,h(t.subMenus,u=>(n(),s(I,v({key:u.title,isSubmenu:!0,ref_for:!0},u),null,16))),128))]),_:2},1032,["label","icon"])):(n(),s(I,v({key:1,ref_for:!0},t),null,16))],64)):V("",!0)],64))),128))]),_:1}))}}),te={class:"q-mr-sm"},ye=k({__name:"AdminLayout",setup(p){const l=S(),{t:e}=T(),i=W(),t=x(),u=Y(),m=P(!1),y=D(()=>[{title:e("user.menu"),icon:"people",subMenus:[{title:e("user.menu"),link:"/admin/dashboard/user"},{title:e("attendance.history"),link:"/admin/dashboard/attendance"},{title:e("payroll.label"),link:"/admin/dashboard/payroll"}]},{title:e("customers"),icon:"person",link:"/admin/dashboard/customer"},{title:e("product.label"),icon:"sym_o_local_mall",subMenus:[{title:e("product.label"),link:"/admin/dashboard/catalog/product"}]},{title:e("order.label"),icon:"shopping_cart",subMenus:[{title:e("onsite"),link:"/admin/dashboard/order/onsite"},{title:e("online"),link:"/admin/dashboard/order/online"}]},{title:e("system"),icon:"settings",subMenus:[{title:e("system"),link:"/admin/dashboard/system"},{title:e("announcement.settings"),link:"/admin/dashboard/announcement"}]},{title:"Xero",icon:"sym_o_account_balance",subMenus:[{title:e("xero.menu.setup"),link:"/admin/dashboard/xero/setup"},{title:e("xero.menu.invoices"),link:"/admin/dashboard/xero/invoices"}]}]),L=()=>{m.value=!m.value},B=()=>{u.showMessage({message:e("logoutConfirm"),timeout:0,ok:async()=>{await O.logout(),t.logout(),l.push("/login")}})};return(oe,r)=>{const A=Q("LanguageSwitcher"),q=Q("router-view");return n(),s(j,{view:"hHr Lpr lFr",class:"shadow-2 rounded-borders"},{default:a(()=>[o(E,{elevated:""},{default:a(()=>[o(U,null,{default:a(()=>[o(f,{flat:"",round:"",dense:"",icon:"menu","aria-label":"Menu",onClick:L}),o(z,null,{default:a(()=>[w(b(g(i).pageTitle),1)]),_:1}),o(f,{flat:"",to:"/order",size:"sm",class:"q-pa-sm"},{default:a(()=>r[2]||(r[2]=[M("div",{class:"text-subtitle1"},"POS",-1)])),_:1}),o(A),M("span",te,b(g(t).getUserName),1),o(f,{flat:"",round:"",dense:"",icon:"logout",onClick:B})]),_:1})]),_:1}),o(H,{modelValue:m.value,"onUpdate:modelValue":r[1]||(r[1]=_=>m.value=_),bordered:"",overlay:""},{default:a(()=>[o(ee,{modelValue:y.value,"onUpdate:modelValue":r[0]||(r[0]=_=>y.value=_)},null,8,["modelValue"])]),_:1},8,["modelValue"]),o($,{style:{"background-color":"#fef9f2"}},{default:a(()=>[o(q)]),_:1})]),_:1})}}});export{ye as default};
