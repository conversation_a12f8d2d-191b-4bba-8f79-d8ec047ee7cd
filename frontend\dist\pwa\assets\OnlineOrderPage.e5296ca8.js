import{Q,a as U}from"./QToolbar.56f560c5.js";import{d as C,u as T,r as d,c as k,s as B,o as g,k as I,f as t,b as a,y as q,p as u,t as r,q as s,Q as M,a as V,F as D,z as N}from"./index.a0917d6f.js";import{Q as P}from"./QSelect.f8fcccb1.js";import{Q as m}from"./QTd.0d1c5436.js";import{Q as Y,f as $}from"./QTr.b57b1e8d.js";import{Q as A}from"./QTable.6af54a73.js";import{Q as H}from"./QPage.30a4ddee.js";import{_ as F,g as L}from"./WCOrderDetailDialog.22a09ed3.js";import{_ as R}from"./TablePagination.6dbfe4cf.js";import{O as z}from"./usePrintInvoice.4dcc6f63.js";import{o as E,c as G}from"./order.ed4ce638.js";import{f as W}from"./date.6d29930c.js";import"./QItemSection.e46fbd88.js";import"./QItemLabel.900eb50d.js";import"./QMenu.5058797d.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";import"./QList.709cd325.js";import"./use-fullscreen.2c828d3b.js";import"./QSpace.b85838fc.js";import"./QScrollArea.05196630.js";import"./QScrollObserver.9d096e23.js";import"./TouchPan.7726afc4.js";import"./use-quasar.ed0f225f.js";import"./xero.1a0fcd7f.js";import"./dialog.bc7c4a9f.js";import"./plugin-vue_export-helper.21dcd24c.js";const Ve=C({__name:"OnlineOrderPage",setup(j){const{t:l}=T(),b=d([]),h=k(()=>[{name:"id",label:l("orderNo"),field:"id",align:"center"},{name:"date_created",label:l("dateAt"),field:"date_created",align:"center"},{name:"customer",label:l("customer.label"),field:"customer_name",align:"center"},{name:"total",label:l("total"),field:"total",align:"center"},{name:"status",label:l("status"),field:"status",align:"center"},{name:"shipping_method",label:l("shippingMethod"),field:"shipping_method",align:"center"}]),n=d({sortBy:"date_created",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),c=d(!1),_=d(""),v=d(""),O=k(()=>[{label:l("allStatus"),value:""},...E.value]),p=async()=>{try{c.value=!0;const i=await z.listWCHistory({pagination:n.value});b.value=i.result.data,n.value=i.result.pagination}finally{c.value=!1}};B(()=>{p()});const f=d(!1),y=d(0),S=i=>{y.value=i,f.value=!0},x=i=>{if(!i)return;const o=i,{sortBy:e,descending:w}=o.pagination;n.value.sortBy=e,n.value.descending=w,p()};return(i,o)=>(g(),I(H,null,{default:t(()=>[a(N,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:t(()=>[a(q,null,{default:t(()=>[a(A,{"virtual-scroll":"",rows:b.value,columns:h.value,pagination:n.value,"onUpdate:pagination":o[2]||(o[2]=e=>n.value=e),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:x,loading:c.value},{top:t(()=>[a(Q,null,{default:t(()=>[a(U,null,{default:t(()=>[u(r(s(l)("orders"))+" - "+r(s(l)("online")),1)]),_:1})]),_:1}),a(Q,{style:{display:"none"}},{default:t(()=>[a(M,{modelValue:_.value,"onUpdate:modelValue":[o[0]||(o[0]=e=>_.value=e),p],label:s(l)("customer.label"),"stack-label":""},null,8,["modelValue","label"]),a(P,{modelValue:v.value,"onUpdate:modelValue":o[1]||(o[1]=e=>v.value=e),options:O.value,"option-label":"label","option-value":"value",label:s(l)("orderStatus.label"),"emit-value":"","map-options":"",class:"q-mx-md",style:{width:"120px"}},null,8,["modelValue","options","label"])]),_:1})]),body:t(e=>[a(Y,{props:e,onClick:w=>S(e.row.id)},{default:t(()=>[a(m,{props:e,key:"id"},{default:t(()=>[u(r(e.row.id),1)]),_:2},1032,["props"]),a(m,{props:e,key:"date_created"},{default:t(()=>[u(r(s(W)(e.row.date_created,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),a(m,{props:e,key:"customer"},{default:t(()=>[e.row.customer_name?(g(),V(D,{key:0},[u(r(e.row.customer_name),1)],64)):(g(),V(D,{key:1},[u(r(s(l)("unknown.customer")),1)],64))]),_:2},1032,["props"]),a(m,{props:e,key:"total",class:"text-bold"},{default:t(()=>[u(" AU$ "+r(s($)(e.row.total,2)),1)]),_:2},1032,["props"]),a(m,{props:e,key:"status",class:"text-bold"},{default:t(()=>[u(r(s(G)(e.row.status)),1)]),_:2},1032,["props"]),a(m,{props:e,key:"shipping_method"},{default:t(()=>[u(r(s(L)(e.row.shipping_method,e.row.shipping_method_title)),1)]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns","pagination","loading"]),a(R,{modelValue:n.value,"onUpdate:modelValue":o[3]||(o[3]=e=>n.value=e),onGetData:p},null,8,["modelValue"])]),_:1})]),_:1}),a(F,{modelValue:f.value,"onUpdate:modelValue":o[4]||(o[4]=e=>f.value=e),orderID:y.value,onRefresh:p},null,8,["modelValue","orderID"])]),_:1}))}});export{Ve as default};
