import{E as L,at as K,au as w,av as R,Z as P,c as l,J as r,T as O,K as Q,az as j,r as u,aw as A,a6 as z,aA as g,w as I,aB as J,ax as M}from"./index.a0917d6f.js";import{a as U,Q as C}from"./QScrollObserver.9d096e23.js";var k=L({name:"QPageContainer",setup(t,{slots:m}){const{proxy:{$q:c}}=Q(),n=K(R,w);if(n===w)return console.error("QPageContainer needs to be child of QLayout"),w;P(j,!0);const d=l(()=>{const i={};return n.header.space===!0&&(i.paddingTop=`${n.header.size}px`),n.right.space===!0&&(i[`padding${c.lang.rtl===!0?"Left":"Right"}`]=`${n.right.size}px`),n.footer.space===!0&&(i.paddingBottom=`${n.footer.size}px`),n.left.space===!0&&(i[`padding${c.lang.rtl===!0?"Right":"Left"}`]=`${n.left.size}px`),i});return()=>r("div",{class:"q-page-container",style:d.value},O(m.default))}}),D=L({name:"QLayout",props:{container:Boolean,view:{type:String,default:"hhh lpr fff",validator:t=>/^(h|l)h(h|r) lpr (f|l)f(f|r)$/.test(t.toLowerCase())},onScroll:Function,onScrollHeight:Function,onResize:Function},setup(t,{slots:m,emit:c}){const{proxy:{$q:n}}=Q(),d=u(null),i=u(n.screen.height),y=u(t.container===!0?0:n.screen.width),S=u({position:0,direction:"down",inflectionPoint:0}),h=u(0),a=u(A.value===!0?0:z()),q=l(()=>"q-layout q-layout--"+(t.container===!0?"containerized":"standard")),H=l(()=>t.container===!1?{minHeight:n.screen.height+"px"}:null),T=l(()=>a.value!==0?{[n.lang.rtl===!0?"left":"right"]:`${a.value}px`}:null),W=l(()=>a.value!==0?{[n.lang.rtl===!0?"right":"left"]:0,[n.lang.rtl===!0?"left":"right"]:`-${a.value}px`,width:`calc(100% + ${a.value}px)`}:null);function F(e){if(t.container===!0||document.qScrollPrevented!==!0){const o={position:e.position.top,direction:e.direction,directionChanged:e.directionChanged,inflectionPoint:e.inflectionPoint.top,delta:e.delta.top};S.value=o,t.onScroll!==void 0&&c("scroll",o)}}function B(e){const{height:o,width:s}=e;let f=!1;i.value!==o&&(f=!0,i.value=o,t.onScrollHeight!==void 0&&c("scrollHeight",o),b()),y.value!==s&&(f=!0,y.value=s),f===!0&&t.onResize!==void 0&&c("resize",e)}function E({height:e}){h.value!==e&&(h.value=e,b())}function b(){if(t.container===!0){const e=i.value>h.value?z():0;a.value!==e&&(a.value=e)}}let v=null;const $={instances:{},view:l(()=>t.view),isContainer:l(()=>t.container),rootRef:d,height:i,containerHeight:h,scrollbarWidth:a,totalWidth:l(()=>y.value+a.value),rows:l(()=>{const e=t.view.toLowerCase().split(" ");return{top:e[0].split(""),middle:e[1].split(""),bottom:e[2].split("")}}),header:g({size:0,offset:0,space:!1}),right:g({size:300,offset:0,space:!1}),footer:g({size:0,offset:0,space:!1}),left:g({size:300,offset:0,space:!1}),scroll:S,animate(){v!==null?clearTimeout(v):document.body.classList.add("q-body--layout-animate"),v=setTimeout(()=>{v=null,document.body.classList.remove("q-body--layout-animate")},155)},update(e,o,s){$[e][o]=s}};if(P(R,$),z()>0){let s=function(){e=null,o.classList.remove("hide-scrollbar")},f=function(){if(e===null){if(o.scrollHeight>n.screen.height)return;o.classList.add("hide-scrollbar")}else clearTimeout(e);e=setTimeout(s,300)},p=function(x){e!==null&&x==="remove"&&(clearTimeout(e),s()),window[`${x}EventListener`]("resize",f)},e=null;const o=document.body;I(()=>t.container!==!0?"add":"remove",p),t.container!==!0&&p("add"),J(()=>{p("remove")})}return()=>{const e=M(m.default,[r(U,{onScroll:F}),r(C,{onResize:B})]),o=r("div",{class:q.value,style:H.value,ref:t.container===!0?void 0:d,tabindex:-1},e);return t.container===!0?r("div",{class:"q-layout-container overflow-hidden",ref:d},[r(C,{onResize:E}),r("div",{class:"absolute-full",style:T.value},[r("div",{class:"scroll",style:W.value},[o])])]):o}}});export{k as Q,D as a};
