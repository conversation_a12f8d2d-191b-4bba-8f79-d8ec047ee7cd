import{d as oe,aF as le,u as se,r as b,aA as L,c as S,s as ne,o as p,k as w,f as t,i as m,b as e,y as V,t as n,h as ie,m as h,p as r,l as U,a as O,z as B,q as N,j as re,F as ue,aH as ce,x as de}from"./index.a0917d6f.js";import{Q as me}from"./QBanner.53361e18.js";import{Q as ve,b as q}from"./QSelect.f8fcccb1.js";import{Q as C}from"./QTd.0d1c5436.js";import{Q as fe}from"./QTooltip.9cf016fd.js";import{Q as pe}from"./QTable.6af54a73.js";import{Q as ge}from"./QSpace.b85838fc.js";import{Q as u}from"./QItemLabel.900eb50d.js";import{Q as v,a as d}from"./QItemSection.e46fbd88.js";import{Q as be}from"./QList.709cd325.js";import{Q as ye}from"./QScrollArea.05196630.js";import{Q as _e}from"./QPage.30a4ddee.js";import{u as xe}from"./use-quasar.ed0f225f.js";import{X as k}from"./xero.1a0fcd7f.js";import{f as E}from"./date.6d29930c.js";import{_ as we}from"./DateRangePicker.478551de.js";import{_ as he}from"./TablePagination.6dbfe4cf.js";import"./QMenu.5058797d.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";import"./use-fullscreen.2c828d3b.js";import"./QScrollObserver.9d096e23.js";import"./TouchPan.7726afc4.js";import"./QDate.e32febd2.js";import"./QPopupProxy.68ef9752.js";import"./ClosePopup.b8d8b91c.js";const De={class:"row justify-center"},Ie={class:"col-12"},Qe={class:"text-h6 q-mb-md"},$e={key:1,class:"row q-gutter-md q-mb-md"},Ue={class:"row q-pt-sm"},ke={class:"text-h6 text-bold"},Ae={class:"q-mb-md bg-white rounded-borders",style:{border:"1px solid #e0e0e0"}},Se={class:"row"},Ce={class:"q-pa-md q-mb-md bg-blue-grey-1 rounded-borders"},Ee={class:"text-h6 text-bold q-mb-md"},Te={class:"row q-gutter-sm"},it=oe({__name:"XeroInvoicesPage",setup(Re){const M=le(),{t:i}=se(),f=xe(),D=b(!1),T=b([]),s=b(null),I=b(!1),R=b(!1),P=b(!1),y=L({connected:!1,tenant_name:""}),g=L({status:"",dateRange:{from:"",to:""}}),c=b({sortBy:"Date",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),j=S(()=>[{label:i("xero.invoices.status.draft"),value:"DRAFT"},{label:i("xero.invoices.status.submitted"),value:"SUBMITTED"},{label:i("xero.invoices.status.authorised"),value:"AUTHORISED"},{label:i("xero.invoices.status.paid"),value:"PAID"},{label:i("xero.invoices.status.voided"),value:"VOIDED"}]),H=S(()=>[{name:"invoiceNumber",label:i("xero.invoices.invoiceNumber"),field:"invoice_number",align:"left",sortable:!0},{name:"type",label:i("xero.invoices.type.label"),field:"type",align:"center"},{name:"contact.name",label:i("xero.invoices.contact"),field:a=>{var l;return((l=a.contact)==null?void 0:l.name)||""},align:"left",sortable:!0},{name:"date",label:i("xero.invoices.date"),field:"date",align:"center",sortable:!0,format:a=>E(a)},{name:"due_date",label:i("xero.invoices.dueDate"),field:"due_date",align:"center",format:a=>E(a)},{name:"status",label:i("status"),field:"status",align:"center",sortable:!0},{name:"total",label:i("xero.invoices.total"),field:"total",align:"right",sortable:!0},{name:"actions",label:i("actions"),field:"",align:"center"}]),z=async()=>{try{const a=await k.getConnectionStatus();Object.assign(y,a.result)}catch(a){console.error("Failed to load connection status:",a)}},Q=async()=>{var a,l,o,$;if(!!y.connected){D.value=!0;try{const x={page:c.value.page,pageSize:c.value.rowsPerPage,status:g.status||void 0,dateFrom:g.dateRange.from||void 0,dateTo:g.dateRange.to||void 0,order:c.value.sortBy||void 0,asc:c.value.descending?"false":"true"},A=await k.getInvoices(x);T.value=((a=A.result)==null?void 0:a.invoices)||[],c.value.page=((l=A.result)==null?void 0:l.page)||1;const te=((o=A.result)==null?void 0:o.has_more)||!1,ae=(($=A.result)==null?void 0:$.count)||0;te?c.value.rowsNumber=c.value.page*c.value.rowsPerPage+1:c.value.rowsNumber=(c.value.page-1)*c.value.rowsPerPage+ae}catch(x){console.error("Failed to load invoices:",x),T.value=[],c.value.rowsNumber=0,f.notify({position:"top",type:"negative",message:(x==null?void 0:x.message)||i("failed")})}finally{D.value=!1}}},Y=a=>{if(!a)return;const l=a,{sortBy:o,descending:$}=l.pagination;c.value.sortBy=o,c.value.descending=$,Q()},X=async a=>{var l;try{D.value=!0;const o=await k.getInvoice(a.invoice_id);s.value=((l=o.result)==null?void 0:l.invoice)||null,s.value?I.value=!0:f.notify({position:"top",type:"negative",message:i("xero.invoices.invoiceNotFound")})}catch(o){console.error("Failed to load invoice details:",o),s.value=null,f.notify({position:"top",type:"negative",message:(o==null?void 0:o.message)||i("failed")})}finally{D.value=!1}},G=()=>{M.push("/admin/dashboard/xero/setup")},F=a=>({DRAFT:"grey",SUBMITTED:"orange",AUTHORISED:"blue",PAID:"positive",VOIDED:"negative"})[a]||"grey",_=(a,l="AUD")=>new Intl.NumberFormat("en-AU",{style:"currency",currency:l}).format(a),W=a=>({DRAFT:i("xero.invoices.status.draft"),SUBMITTED:i("xero.invoices.status.submitted"),AUTHORISED:i("xero.invoices.status.authorised"),PAID:i("xero.invoices.status.paid"),VOIDED:i("xero.invoices.status.voided")})[a]||a,J=S(()=>s.value?["AUTHORISED","PAID"].includes(s.value.status):!1),K=S(()=>{var a;return s.value?["AUTHORISED","PAID"].includes(s.value.status)&&((a=s.value.contact)==null?void 0:a.name):!1}),Z=async()=>{if(!!s.value)try{R.value=!0;const a=await k.getInvoicePDF(s.value.invoice_id);if(a.size<100)throw new Error("PDF data too small, likely invalid");const l=URL.createObjectURL(a),o=window.open(l,"_blank");if(!o)throw URL.revokeObjectURL(l),new Error("Failed to open print window - popup blocked?");o.onload=()=>{setTimeout(()=>{o.print()},1e3)},setTimeout(()=>{URL.revokeObjectURL(l)},15e3),f.notify({type:"positive",message:i("printInvoiceSuccess"),position:"top"})}catch(a){console.error("Print invoice error:",a),f.notify({type:"negative",message:(a==null?void 0:a.message)||i("printInvoiceError"),position:"top"})}finally{R.value=!1}},ee=async()=>{var a;!s.value||!((a=s.value.contact)!=null&&a.name)||f.dialog({title:i("sendEmail"),message:i("xero.invoices.enterEmailAddress"),prompt:{model:"",type:"email",placeholder:"<EMAIL>"},cancel:!0,persistent:!0}).onOk(async l=>{if(!(!l||!s.value))try{P.value=!0,await k.sendInvoiceEmail(s.value.invoice_id,l),f.notify({type:"positive",message:i("sendEmailSuccess"),position:"top"})}catch(o){console.error("Send email error:",o),f.notify({type:"negative",message:(o==null?void 0:o.message)||i("sendEmailError"),position:"top"})}finally{P.value=!1}})};return ne(async()=>{await z(),y.connected&&await Q()}),(a,l)=>(p(),w(_e,{class:"q-pa-md"},{default:t(()=>[m("div",De,[m("div",Ie,[e(B,null,{default:t(()=>[e(V,null,{default:t(()=>[m("div",Qe,n(a.$t("xero.invoices.title")),1),y.connected?U("",!0):(p(),w(me,{key:0,class:"bg-warning text-dark q-mb-md",rounded:""},{avatar:t(()=>[e(ie,{name:"warning",color:"orange"})]),action:t(()=>[e(h,{flat:"",color:"dark",label:a.$t("xero.invoices.goToSetup"),onClick:G},null,8,["label"])]),default:t(()=>[r(" "+n(a.$t("xero.invoices.notConnected"))+" ",1)]),_:1})),y.connected?(p(),O("div",$e,[e(ve,{modelValue:g.status,"onUpdate:modelValue":[l[0]||(l[0]=o=>g.status=o),Q],options:j.value,label:a.$t("status"),clearable:"",style:{"min-width":"150px"},"map-options":"","emit-value":"",dense:""},null,8,["modelValue","options","label"]),e(we,{modelValue:g.dateRange,"onUpdate:modelValue":[l[1]||(l[1]=o=>g.dateRange=o),Q],dateMask:"YYYY-MM-DD"},null,8,["modelValue"])])):U("",!0),y.connected?(p(),w(pe,{key:2,rows:T.value,columns:H.value,pagination:c.value,"onUpdate:pagination":l[2]||(l[2]=o=>c.value=o),"hide-pagination":"",onRequest:Y,"row-key":"invoice_id","binary-state-sort":"",loading:D.value},{"body-cell-status":t(o=>[e(C,{props:o},{default:t(()=>[e(q,{color:F(o.value),"text-color":"white",label:a.$t(`xero.invoices.status.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-type":t(o=>[e(C,{props:o},{default:t(()=>[e(q,{color:o.value==="ACCREC"?"positive":"info","text-color":"white",label:a.$t(`xero.invoices.type.${o.value.toLowerCase()}`)},null,8,["color","label"])]),_:2},1032,["props"])]),"body-cell-total":t(o=>[e(C,{props:o},{default:t(()=>[r(" AU "+n(_(o.value,o.row.currency_code)),1)]),_:2},1032,["props"])]),"body-cell-actions":t(o=>[e(C,{props:o},{default:t(()=>[e(h,{flat:"",round:"",color:"primary",icon:"visibility",size:"sm",onClick:$=>X(o.row)},{default:t(()=>[e(fe,null,{default:t(()=>[r(n(a.$t("view")),1)]),_:1})]),_:2},1032,["onClick"])]),_:2},1032,["props"])]),_:1},8,["rows","columns","pagination","loading"])):U("",!0),e(he,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=o=>c.value=o),onGetData:Q},null,8,["modelValue"])]),_:1})]),_:1})])]),e(de,{modelValue:I.value,"onUpdate:modelValue":l[6]||(l[6]=o=>I.value=o),"no-refocus":"",class:"card-dialog"},{default:t(()=>[e(B,{class:"column"},{default:t(()=>[e(V,{class:"col-1 q-py-none"},{default:t(()=>[m("div",Ue,[m("div",ke,n(a.$t("xero.invoices.invoiceDetails")),1),e(ge),e(h,{type:"button",icon:"close",flat:"",round:"",dense:"",onClick:l[4]||(l[4]=o=>I.value=!1)})])]),_:1}),s.value?(p(),w(V,{key:0,class:"col-10"},{default:t(()=>[e(ye,{class:"full-height"},{default:t(()=>[m("div",Ae,[m("div",Se,[s.value.invoice_number?(p(),w(v,{key:0,class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.invoiceNumber")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(n(s.value.invoice_number),1)]),_:1})]),_:1})]),_:1})):U("",!0),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("status")),1)]),_:1}),e(u,null,{default:t(()=>[e(q,{color:F(s.value.status),"text-color":"white",class:"text-subtitle1",size:"md"},{default:t(()=>[r(n(W(s.value.status)),1)]),_:1},8,["color"])]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.contact")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>{var o;return[r(n(((o=s.value.contact)==null?void 0:o.name)||""),1)]}),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.date")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(n(N(E)(s.value.date)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.dueDate")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r(n(N(E)(s.value.due_date)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.subTotal")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r("AU "+n(_(s.value.sub_total,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12 col-md-6"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.totalTax")),1)]),_:1}),e(u,{class:"text-subtitle1 text-grey-9"},{default:t(()=>[r("AU "+n(_(s.value.total_tax,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1}),e(v,{class:"col-12"},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-weight-medium text-grey-7 text-caption"},{default:t(()=>[r(n(a.$t("xero.invoices.total")),1)]),_:1}),e(u,{class:"text-h6 text-primary text-weight-bold"},{default:t(()=>[r("AU "+n(_(s.value.total,s.value.currency_code)),1)]),_:1})]),_:1})]),_:1})])]),m("div",Ce,[m("div",Ee,n(a.$t("xero.invoices.lineItems")),1),e(be,{bordered:"",separator:""},{default:t(()=>[e(v,{class:"text-bold text-subtitle1 bg-grey-3"},{default:t(()=>[e(d,null,{default:t(()=>[r(n(a.$t("product.label")),1)]),_:1}),e(d,{side:""},{default:t(()=>[r(n(a.$t("price")),1)]),_:1})]),_:1}),(p(!0),O(ue,null,re(s.value.line_items,o=>(p(),w(v,{key:o.line_item_id},{default:t(()=>[e(d,null,{default:t(()=>[e(u,{class:"text-subtitle1 text-weight-bold"},{default:t(()=>[r(n(o.description),1)]),_:2},1024),e(u,{class:"text-subtitle2"},{default:t(()=>[r(n(o.quantity)+" x AU "+n(_(o.unit_amount,s.value.currency_code)),1)]),_:2},1024)]),_:2},1024),e(d,{side:""},{default:t(()=>[e(u,{class:"text-subtitle1 text-weight-bold"},{default:t(()=>[r(" AU "+n(_(o.line_amount,s.value.currency_code)),1)]),_:2},1024)]),_:2},1024)]),_:2},1024))),128))]),_:1})])]),_:1})]),_:1})):U("",!0),e(ce,{align:"between",class:"col-1 bg-grey-2 q-pa-sm"},{default:t(()=>[m("div",Te,[e(h,{color:"primary",icon:"print",label:a.$t("printInvoice"),onClick:Z,loading:R.value,disable:!J.value},null,8,["label","loading","disable"]),e(h,{color:"secondary",icon:"email",label:a.$t("sendEmail"),onClick:ee,loading:P.value,disable:!K.value},null,8,["label","loading","disable"])]),e(h,{color:"negative",icon:"close",label:a.$t("close"),onClick:l[5]||(l[5]=o=>I.value=!1)},null,8,["label"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),_:1}))}});export{it as default};
