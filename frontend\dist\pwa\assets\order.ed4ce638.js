import{c as t,by as e}from"./index.a0917d6f.js";const c=t(()=>[{label:e.global.t("orderVoided.reasons.outOfStock"),value:"Out of Stock"},{label:e.global.t("orderVoided.reasons.duplicateOrder"),value:"Duplicate Order"},{label:e.global.t("orderVoided.reasons.incorrectInfo"),value:"Incorrect Information"},{label:e.global.t("orderVoided.reasons.customerReject"),value:"Customer Reject"},{label:e.global.t("orderVoided.reasons.other"),value:"other"}]),b=t(()=>[{label:e.global.t("orderStatus.processing"),value:"processing"},{label:e.global.t("orderStatus.completed"),value:"completed"},{label:e.global.t("orderStatus.cancelled"),value:"cancelled"},{label:e.global.t("orderStatus.void"),value:"void"}]),a=t(()=>({pending:{textColor:"black",label:e.global.t("orderStatus.pending")},"on-hold":{textColor:"black",label:e.global.t("orderStatus.on-hold")},processing:{textColor:"black",label:e.global.t("orderStatus.processing")},packing:{textColor:"black",label:e.global.t("orderStatus.packing")},shipping:{textColor:"black",label:e.global.t("orderStatus.shipping")},completed:{textColor:"black",label:e.global.t("orderStatus.completed")},cancelled:{color:"bg-red-1",textColor:"red-2",label:e.global.t("orderStatus.cancelled")},refunded:{color:"bg-red-1",textColor:"red-2",label:e.global.t("orderStatus.refunded")},void:{color:"bg-red-1",textColor:"red-2",label:e.global.t("orderStatus.void")}})),r={cash:"green",credit:"blue",paypal:"purple"},d=t(()=>({cash:e.global.t("payment.cash"),credit:e.global.t("payment.creditCard"),paypal:e.global.t("payment.paypal")})),n=l=>{var o;return((o=a.value[l])==null?void 0:o.color)||""},u=l=>{var o;return((o=a.value[l])==null?void 0:o.label)||l},g=l=>r[l]||"grey",p=l=>d.value[l]||l;export{g as a,n as b,u as c,p as g,b as o,c as v};
