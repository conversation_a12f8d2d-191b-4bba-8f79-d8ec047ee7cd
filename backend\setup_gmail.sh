#!/bin/bash

echo "Gmail API 快速設置工具"
echo "========================"
echo ""
echo "請選擇設置方式:"
echo "1. 獲取授權 URL (需要手動在瀏覽器中完成授權)"
echo "2. 啟動授權服務器 (自動化授權流程)"
echo "3. 檢查當前授權狀態"
echo "4. 測試發送郵件"
echo ""

read -p "請輸入選項 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "正在獲取授權 URL..."
        go run cmd/gmail_auth/main.go auth
        echo ""
        echo "請複製上面的 URL 到瀏覽器中完成授權"
        echo "授權完成後，使用以下命令獲取 token:"
        echo "go run cmd/gmail_auth/main.go token YOUR_AUTHORIZATION_CODE"
        ;;
    2)
        echo ""
        echo "正在啟動授權服務器..."
        echo "請在瀏覽器中訪問: http://localhost:8081/auth"
        go run cmd/gmail_auth/main.go server
        ;;
    3)
        echo ""
        echo "正在檢查授權狀態..."
        curl -s http://localhost:8080/au-pos/api/v1/gmail/status | jq .
        echo ""
        ;;
    4)
        echo ""
        read -p "請輸入測試郵件地址: " email
        echo "正在發送測試郵件到 $email..."
        curl -X POST http://localhost:8080/au-pos/api/v1/gmail/test-email \
          -H "Content-Type: application/json" \
          -d "{\"to\": \"$email\", \"subject\": \"Gmail API 測試\", \"body\": \"這是一封測試郵件，Gmail API 設置成功！\"}" | jq .
        echo ""
        ;;
    *)
        echo "無效的選項，請重新運行腳本"
        ;;
esac

echo ""
echo "設置完成！"
