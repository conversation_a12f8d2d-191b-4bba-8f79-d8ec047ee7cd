@echo off
echo Gmail API 快速設置工具
echo ========================

echo.
echo 請選擇設置方式:
echo 1. 獲取授權 URL (需要手動在瀏覽器中完成授權)
echo 2. 啟動授權服務器 (自動化授權流程)
echo 3. 檢查當前授權狀態
echo 4. 測試發送郵件
echo.

set /p choice="請輸入選項 (1-4): "

if "%choice%"=="1" goto get_auth_url
if "%choice%"=="2" goto start_server
if "%choice%"=="3" goto check_status
if "%choice%"=="4" goto test_email
goto invalid_choice

:get_auth_url
echo.
echo 正在獲取授權 URL...
go run cmd/gmail_auth/main.go auth
echo.
echo 請複製上面的 URL 到瀏覽器中完成授權
echo 授權完成後，使用以下命令獲取 token:
echo go run cmd/gmail_auth/main.go token YOUR_AUTHORIZATION_CODE
goto end

:start_server
echo.
echo 正在啟動授權服務器...
echo 請在瀏覽器中訪問: http://localhost:8081/auth
go run cmd/gmail_auth/main.go server
goto end

:check_status
echo.
echo 正在檢查授權狀態...
curl -s http://localhost:8080/au-pos/api/v1/gmail/status
echo.
goto end

:test_email
echo.
set /p email="請輸入測試郵件地址: "
echo 正在發送測試郵件到 %email%...
curl -X POST http://localhost:8080/au-pos/api/v1/gmail/test-email ^
  -H "Content-Type: application/json" ^
  -d "{\"to\": \"%email%\", \"subject\": \"Gmail API 測試\", \"body\": \"這是一封測試郵件，Gmail API 設置成功！\"}"
echo.
goto end

:invalid_choice
echo 無效的選項，請重新運行腳本
goto end

:end
echo.
echo 設置完成！
pause
