import{Q as f,a as q}from"./QToolbar.56f560c5.js";import{d as N,u as P,r as s,c as y,s as $,o as w,k as A,f as l,b as t,y as H,p as d,t as i,q as m,Q as K,bw as R,g as F,h as L,a as O,F as U,z}from"./index.a0917d6f.js";import{Q as C}from"./QSelect.f8fcccb1.js";import{Q as p}from"./QTd.0d1c5436.js";import{f as E,Q as G}from"./QTr.b57b1e8d.js";import{Q as j}from"./QTable.6af54a73.js";import{Q as J}from"./QPage.30a4ddee.js";import{C as W}from"./customer.d6a956dc.js";import{O as X}from"./usePrintInvoice.4dcc6f63.js";import{o as Z,c as ee}from"./order.ed4ce638.js";import{f as S}from"./date.6d29930c.js";import{_ as ae}from"./DateRangePicker.478551de.js";import{_ as te}from"./OrderDetailDialog.56530145.js";import{_ as oe}from"./TablePagination.6dbfe4cf.js";import"./QItemSection.e46fbd88.js";import"./QItemLabel.900eb50d.js";import"./QMenu.5058797d.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";import"./QList.709cd325.js";import"./use-fullscreen.2c828d3b.js";import"./plugin-vue_export-helper.21dcd24c.js";import"./xero.1a0fcd7f.js";import"./QDate.e32febd2.js";import"./QPopupProxy.68ef9752.js";import"./ClosePopup.b8d8b91c.js";import"./QSpace.b85838fc.js";import"./QScrollArea.05196630.js";import"./QScrollObserver.9d096e23.js";import"./TouchPan.7726afc4.js";import"./use-quasar.ed0f225f.js";import"./useOrder.6a5cbe5a.js";const Ne=N({__name:"OnsiteOrderPage",setup(le){const{t:r}=P(),V=s([]),c=s({from:"",to:""}),Y=y(()=>[{name:"order_no",label:r("orderNo"),field:"order_no",align:"center"},{name:"order_at",label:r("dateAt"),field:o=>S(o.order_at,"YYYY-MM-DD HH:mm"),align:"center",sortable:!0},{name:"customer",label:r("customer.label"),field:o=>{var a;return((a=o.customer)==null?void 0:a.name)||r("unknown.customer")},align:"center"},{name:"total",label:r("total"),field:o=>E(o.total,2),align:"center",sortable:!0},{name:"status",label:r("status"),field:o=>r(o.status),align:"center",sortable:!0}]),n=s({sortBy:"order_at",descending:!0,page:1,rowsPerPage:10,rowsNumber:0}),v=s(!1),b=s(""),g=s(""),Q=s(""),h=y(()=>[{label:r("allStatus"),value:""},...Z.value]),u=async()=>{try{v.value=!0;const o=await X.fetch({filter:{search:b.value,customer_uuid:g.value,start_date:c.value.from,end_date:c.value.to,exclude_status:["pending"]},pagination:n.value});V.value=o.result.data,n.value=o.result.pagination}finally{v.value=!1}},k=s([]),I=y(()=>[{uuid:"",name:r("customer.all")},...k.value]),T=async()=>{const o=await W.fetch();for(let a of o.result.data)k.value.push({uuid:a.uuid,name:a.name})};$(()=>{u(),T()});const _=s(!1),D=s(""),B=o=>{D.value=o,_.value=!0},M=o=>{if(!o)return;const a=o,{sortBy:e,descending:x}=a.pagination;n.value.sortBy=e,n.value.descending=x,u()};return(o,a)=>(w(),A(J,null,{default:l(()=>[t(z,{flat:"",square:"",bordered:"",class:"bg-cream"},{default:l(()=>[t(H,null,{default:l(()=>[t(j,{"virtual-scroll":"",rows:V.value,columns:Y.value,pagination:n.value,"onUpdate:pagination":a[4]||(a[4]=e=>n.value=e),"hide-pagination":"","binary-state-sort":"","table-header-class":"bg-grey-3",onRequest:M,loading:v.value},{top:l(()=>[t(f,null,{default:l(()=>[t(q,null,{default:l(()=>[d(i(m(r)("orders"))+" - "+i(m(r)("onsite")),1)]),_:1})]),_:1}),t(f,null,{default:l(()=>[t(ae,{modelValue:c.value,"onUpdate:modelValue":[a[0]||(a[0]=e=>c.value=e),u]},null,8,["modelValue"])]),_:1}),t(f,null,{default:l(()=>[t(K,{modelValue:b.value,"onUpdate:modelValue":[a[1]||(a[1]=e=>b.value=e),u],outlined:"",dense:"",placeholder:m(r)("search.order"),clearable:"","clear-icon":"close",onKeyup:R(F(u,["prevent"]),["enter"]),"input-debounce":"500",style:{width:"300px"}},{prepend:l(()=>[t(L,{name:"search"})]),_:1},8,["modelValue","placeholder","onKeyup"])]),_:1}),t(f,null,{default:l(()=>[t(C,{modelValue:g.value,"onUpdate:modelValue":[a[2]||(a[2]=e=>g.value=e),u],options:I.value,"option-label":"name","option-value":"uuid",label:m(r)("customer.label"),"emit-value":"","map-options":""},null,8,["modelValue","options","label"]),t(C,{modelValue:Q.value,"onUpdate:modelValue":a[3]||(a[3]=e=>Q.value=e),options:h.value,"option-label":"label","option-value":"value",label:m(r)("orderStatus.label"),"emit-value":"","map-options":"",class:"q-mx-md",style:{width:"120px"}},null,8,["modelValue","options","label"])]),_:1})]),body:l(e=>[t(G,{props:e,onClick:x=>B(e.row.uuid)},{default:l(()=>[t(p,{props:e,key:"order_no"},{default:l(()=>[d(i(e.row.order_no),1)]),_:2},1032,["props"]),t(p,{props:e,key:"order_at"},{default:l(()=>[d(i(m(S)(e.row.order_at,"YYYY-MM-DD HH:mm")),1)]),_:2},1032,["props"]),t(p,{props:e,key:"customer"},{default:l(()=>[e.row.customer.name?(w(),O(U,{key:0},[d(i(e.row.customer.name),1)],64)):(w(),O(U,{key:1},[d(i(m(r)("unknown.customer")),1)],64))]),_:2},1032,["props"]),t(p,{props:e,key:"total",class:"text-bold"},{default:l(()=>[d(" AU$ "+i(e.row.total),1)]),_:2},1032,["props"]),t(p,{props:e,key:"status",class:"text-bold"},{default:l(()=>[d(i(m(ee)(e.row.status)),1)]),_:2},1032,["props"])]),_:2},1032,["props","onClick"])]),_:1},8,["rows","columns","pagination","loading"]),t(oe,{modelValue:n.value,"onUpdate:modelValue":a[5]||(a[5]=e=>n.value=e),onGetData:u},null,8,["modelValue"])]),_:1})]),_:1}),t(te,{modelValue:_.value,"onUpdate:modelValue":a[6]||(a[6]=e=>_.value=e),orderID:D.value,onRefresh:u},null,8,["modelValue","orderID"])]),_:1}))}});export{Ne as default};
