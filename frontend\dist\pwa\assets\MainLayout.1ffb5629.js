import{d as Q,aF as b,aG as h,u as w,r as C,c as y,n as l,o as L,k as M,f as o,b as e,m as r,p as V,t as u,q as i,i as x,A as T}from"./index.a0917d6f.js";import{Q as q,a as A}from"./QToolbar.56f560c5.js";import{Q as B}from"./QHeader.fff4b8ce.js";import{Q as N}from"./QDrawer.4d13cda9.js";import{Q as S,a as D}from"./QLayout.ecd06863.js";import{u as I}from"./pageInfo.9ba74675.js";import{u as F}from"./dialog.bc7c4a9f.js";import"./QScrollObserver.9d096e23.js";import"./TouchPan.7726afc4.js";import"./selection.6b4c5528.js";import"./format.054b8074.js";const H={class:"q-mr-sm"},Z=Q({__name:"MainLayout",setup(P){const m=b(),d=I(),s=h(),c=F(),{t:a}=w(),t=C(!1),p=y(()=>[{title:a("attendance.label"),link:"/attendance"},{title:a("order.label"),link:"/order"}]),f=()=>{t.value=!t.value},g=()=>{c.showMessage({message:a("logoutConfirm"),timeout:0,ok:async()=>{await T.logout(),s.logout(),m.push("/login")}})};return(U,n)=>{const _=l("MenuLink"),k=l("router-view");return L(),M(D,{view:"hHr Lpr lFr",class:"shadow-2 rounded-borders"},{default:o(()=>[e(B,{elevated:""},{default:o(()=>[e(q,null,{default:o(()=>[e(r,{flat:"",round:"",dense:"",icon:"menu","aria-label":"Menu",onClick:f}),e(A,null,{default:o(()=>[V(u(i(d).pageTitle),1)]),_:1}),x("span",H,u(i(s).getUserName),1),e(r,{flat:"",round:"",dense:"",icon:"logout",onClick:g})]),_:1})]),_:1}),e(N,{modelValue:t.value,"onUpdate:modelValue":n[0]||(n[0]=v=>t.value=v),bordered:"",overlay:""},{default:o(()=>[e(_,{"link-list":p.value},null,8,["link-list"])]),_:1},8,["modelValue"]),e(S,{class:"q-mt-lg q-px-md",style:{"background-color":"#fef9f2"}},{default:o(()=>[e(k)]),_:1})]),_:1})}}});export{Z as default};
