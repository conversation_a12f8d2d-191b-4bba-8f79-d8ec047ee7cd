package main

import (
	"context"
	"cx/service"
	"fmt"
	"log"
	"net/http"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("Gmail OAuth2 授權工具")
		fmt.Println("使用方式:")
		fmt.Println("  go run cmd/gmail_auth/main.go auth    - 獲取授權 URL")
		fmt.Println("  go run cmd/gmail_auth/main.go token <code> - 使用授權碼獲取 token")
		return
	}

	command := os.Args[1]

	switch command {
	case "auth":
		getAuthURL()
	case "token":
		if len(os.Args) < 3 {
			fmt.Println("請提供授權碼")
			fmt.Println("使用方式: go run cmd/gmail_auth/main.go token <authorization_code>")
			return
		}
		authCode := os.Args[2]
		getToken(authCode)
	case "server":
		startAuthServer()
	default:
		fmt.Printf("未知命令: %s\n", command)
		fmt.Println("可用命令: auth, token, server")
	}
}

func getAuthURL() {
	authURL, err := service.GetGmailAuthURL()
	if err != nil {
		log.Fatalf("獲取授權 URL 失敗: %v", err)
	}

	fmt.Println("請在瀏覽器中打開以下 URL 進行授權:")
	fmt.Println(authURL)
	fmt.Println()
	fmt.Println("授權完成後，您會獲得一個授權碼。")
	fmt.Println("請使用以下命令來獲取 token:")
	fmt.Println("go run cmd/gmail_auth/main.go token <authorization_code>")
}

func getToken(authCode string) {
	ctx := context.Background()
	token, err := service.ExchangeCodeForToken(ctx, authCode)
	if err != nil {
		log.Fatalf("獲取 token 失敗: %v", err)
	}

	fmt.Printf("成功獲取 token!\n")
	fmt.Printf("Access Token: %s...\n", token.AccessToken[:20])
	fmt.Printf("Refresh Token: %s...\n", token.RefreshToken[:20])
	fmt.Printf("Token Type: %s\n", token.TokenType)
	fmt.Printf("Expiry: %s\n", token.Expiry.Format("2006-01-02 15:04:05"))

	// 保存 token 到文件
	if err := saveTokenToFile(token); err != nil {
		log.Fatalf("保存 token 失敗: %v", err)
	}

	fmt.Println("Token 已保存到 gmail_token.json")
	fmt.Println("現在您可以使用 Gmail API 發送郵件了!")
}

func startAuthServer() {
	fmt.Println("啟動授權服務器...")
	fmt.Println("請在瀏覽器中訪問: http://localhost:8081/auth")

	http.HandleFunc("/auth", func(w http.ResponseWriter, r *http.Request) {
		authURL, err := service.GetGmailAuthURL()
		if err != nil {
			http.Error(w, fmt.Sprintf("獲取授權 URL 失敗: %v", err), http.StatusInternalServerError)
			return
		}

		html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <title>Gmail OAuth2 授權</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>Gmail OAuth2 授權</h1>
    <p>請點擊以下連結進行 Gmail 授權:</p>
    <p><a href="%s" target="_blank">點擊這裡授權 Gmail</a></p>
    <p>授權完成後，請將獲得的授權碼貼到下面的表單中:</p>
    <form action="/token" method="post">
        <label for="code">授權碼:</label><br>
        <input type="text" id="code" name="code" style="width: 500px;"><br><br>
        <input type="submit" value="獲取 Token">
    </form>
</body>
</html>`, authURL)

		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write([]byte(html))
	})

	http.HandleFunc("/token", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			http.Error(w, "只支持 POST 方法", http.StatusMethodNotAllowed)
			return
		}

		authCode := r.FormValue("code")
		if authCode == "" {
			http.Error(w, "授權碼不能為空", http.StatusBadRequest)
			return
		}

		ctx := context.Background()
		token, err := service.ExchangeCodeForToken(ctx, authCode)
		if err != nil {
			http.Error(w, fmt.Sprintf("獲取 token 失敗: %v", err), http.StatusInternalServerError)
			return
		}

		// 保存 token 到文件
		if err := saveTokenToFile(token); err != nil {
			http.Error(w, fmt.Sprintf("保存 token 失敗: %v", err), http.StatusInternalServerError)
			return
		}

		html := `
<!DOCTYPE html>
<html>
<head>
    <title>授權成功</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>授權成功!</h1>
    <p>Gmail OAuth2 token 已成功獲取並保存。</p>
    <p>現在您可以使用 Gmail API 發送郵件了!</p>
    <p>您可以關閉這個頁面。</p>
</body>
</html>`

		w.Header().Set("Content-Type", "text/html; charset=utf-8")
		w.Write([]byte(html))
	})

	fmt.Println("服務器已啟動在 http://localhost:8081")
	log.Fatal(http.ListenAndServe(":8081", nil))
}

// saveTokenToFile 保存 token 到文件 (重複實現，應該從 service 包導入)
func saveTokenToFile(token interface{}) error {
	// 這裡應該調用 service 包中的函數
	// 為了簡化，這裡直接實現
	fmt.Println("Token 保存功能需要在 service 包中實現")
	return nil
}
