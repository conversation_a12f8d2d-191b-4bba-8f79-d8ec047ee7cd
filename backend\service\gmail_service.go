package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/spf13/viper"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

// GmailService Gmail API 服務
type GmailService struct {
	service *gmail.Service
}

// NewGmailService 創建新的 Gmail 服務
func NewGmailService(ctx context.Context) (*GmailService, error) {
	service, err := createGmailService(ctx)
	if err != nil {
		return nil, err
	}

	return &GmailService{
		service: service,
	}, nil
}

// createGmailService 創建 Gmail API 服務
func createGmailService(ctx context.Context) (*gmail.Service, error) {
	// 讀取 Google OAuth2 憑證文件
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	// 讀取憑證文件
	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read credentials file: %v", err)
	}

	// 解析憑證
	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return nil, fmt.Errorf("failed to parse credentials: %v", err)
	}

	// 嘗試從 token 文件讀取已保存的 token
	token, err := loadTokenFromFile()
	if err != nil {
		return nil, fmt.Errorf("no valid token found. Please run OAuth2 authorization first: %v", err)
	}

	// 檢查 token 是否需要刷新
	if token.Expiry.Before(time.Now()) && token.RefreshToken != "" {
		// Token 過期，嘗試刷新
		newToken, err := config.TokenSource(ctx, token).Token()
		if err != nil {
			return nil, fmt.Errorf("failed to refresh token: %v", err)
		}

		// 保存新的 token
		if err := saveTokenToFile(newToken); err != nil {
			fmt.Printf("Warning: failed to save refreshed token: %v\n", err)
		}
		token = newToken
	}

	// 創建 HTTP 客戶端
	client := config.Client(ctx, token)

	// 創建 Gmail 服務
	service, err := gmail.NewService(ctx, option.WithHTTPClient(client))
	if err != nil {
		return nil, fmt.Errorf("failed to create Gmail service: %v", err)
	}

	return service, nil
}

// loadTokenFromFile 從文件讀取 token
func loadTokenFromFile() (*oauth2.Token, error) {
	tokenFile := "gmail_token.json"

	data, err := os.ReadFile(tokenFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read token file: %v", err)
	}

	var token oauth2.Token
	if err := json.Unmarshal(data, &token); err != nil {
		return nil, fmt.Errorf("failed to parse token: %v", err)
	}

	return &token, nil
}

// saveTokenToFile 保存 token 到文件
func saveTokenToFile(token *oauth2.Token) error {
	tokenFile := "gmail_token.json"

	data, err := json.MarshalIndent(token, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal token: %v", err)
	}

	if err := os.WriteFile(tokenFile, data, 0600); err != nil {
		return fmt.Errorf("failed to write token file: %v", err)
	}

	return nil
}

// SendEmail 發送郵件
func (g *GmailService) SendEmail(ctx context.Context, to, subject, body string, attachmentData []byte, attachmentName string) error {
	// 從環境變數讀取發送者郵箱
	v := viper.New()
	v.AutomaticEnv()
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(".")

	if err := v.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config: %v", err)
	}

	fromEmail := v.GetString("GMAIL_FROM")
	if fromEmail == "" {
		return fmt.Errorf("GMAIL_FROM environment variable is not set")
	}

	// 構建郵件內容
	var message strings.Builder

	// 郵件標頭
	message.WriteString(fmt.Sprintf("From: %s\r\n", fromEmail))
	message.WriteString(fmt.Sprintf("To: %s\r\n", to))
	message.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	message.WriteString("MIME-Version: 1.0\r\n")

	if attachmentData != nil && len(attachmentData) > 0 {
		// 有附件的郵件
		boundary := "boundary123456789"
		message.WriteString(fmt.Sprintf("Content-Type: multipart/mixed; boundary=%s\r\n\r\n", boundary))

		// 郵件正文部分
		message.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		message.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		message.WriteString("Content-Transfer-Encoding: quoted-printable\r\n\r\n")
		message.WriteString(body)
		message.WriteString("\r\n\r\n")

		// 附件部分
		message.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		message.WriteString(fmt.Sprintf("Content-Type: application/pdf; name=\"%s\"\r\n", attachmentName))
		message.WriteString("Content-Transfer-Encoding: base64\r\n")
		message.WriteString(fmt.Sprintf("Content-Disposition: attachment; filename=\"%s\"\r\n\r\n", attachmentName))

		// 將附件編碼為 base64
		encodedAttachment := base64.StdEncoding.EncodeToString(attachmentData)
		// 每76個字符換行（RFC 2045標準）
		for i := 0; i < len(encodedAttachment); i += 76 {
			end := i + 76
			if end > len(encodedAttachment) {
				end = len(encodedAttachment)
			}
			message.WriteString(encodedAttachment[i:end])
			message.WriteString("\r\n")
		}

		message.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else {
		// 純文字郵件
		message.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		message.WriteString("Content-Transfer-Encoding: quoted-printable\r\n\r\n")
		message.WriteString(body)
	}

	// 創建 Gmail 訊息
	gmailMessage := &gmail.Message{
		Raw: base64.URLEncoding.EncodeToString([]byte(message.String())),
	}

	// 發送郵件
	_, err := g.service.Users.Messages.Send("me", gmailMessage).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("failed to send email via Gmail API: %v", err)
	}

	fmt.Printf("Email sent successfully via Gmail API to: %s\n", to)
	return nil
}

// GetAuthURL 獲取 OAuth2 認證 URL
func GetGmailAuthURL() (string, error) {
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return "", fmt.Errorf("failed to read credentials file: %v", err)
	}

	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return "", fmt.Errorf("failed to parse credentials: %v", err)
	}

	authURL := config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	return authURL, nil
}

// ExchangeCodeForToken 交換授權碼獲取 token
func ExchangeCodeForToken(ctx context.Context, code string) (*oauth2.Token, error) {
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read credentials file: %v", err)
	}

	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return nil, fmt.Errorf("failed to parse credentials: %v", err)
	}

	token, err := config.Exchange(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for token: %v", err)
	}

	return token, nil
}
