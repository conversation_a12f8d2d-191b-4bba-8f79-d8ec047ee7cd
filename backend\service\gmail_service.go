package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"os"
	"strings"

	"github.com/spf13/viper"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/option"
)

// GmailService Gmail API 服務
type GmailService struct {
	service *gmail.Service
}

// NewGmailService 創建新的 Gmail 服務
func NewGmailService(ctx context.Context) (*GmailService, error) {
	service, err := createGmailService(ctx)
	if err != nil {
		return nil, err
	}

	return &GmailService{
		service: service,
	}, nil
}

// createGmailService 創建 Gmail API 服務
func createGmailService(ctx context.Context) (*gmail.Service, error) {
	// 讀取 Google OAuth2 憑證文件
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	// 讀取憑證文件
	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read credentials file: %v", err)
	}

	// 解析憑證
	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return nil, fmt.Errorf("failed to parse credentials: %v", err)
	}

	// 這裡我們需要一個有效的 OAuth2 token
	// 在實際應用中，您需要實現 OAuth2 流程來獲取 token
	// 暫時我們使用一個簡化的方法

	// 嘗試使用應用程式預設憑證
	service, err := gmail.NewService(ctx, option.WithCredentialsFile(credentialsFile), option.WithScopes(gmail.GmailSendScope))
	if err != nil {
		// 如果失敗，嘗試使用 OAuth2 配置
		// 注意：這需要有效的 refresh token 或 access token
		fmt.Printf("Failed to create service with credentials file: %v\n", err)

		// 創建一個假的 token 來演示結構
		// 在實際應用中，您需要從資料庫或其他安全存儲中獲取真實的 token
		token := &oauth2.Token{
			AccessToken:  "your_access_token_here",
			RefreshToken: "your_refresh_token_here",
			TokenType:    "Bearer",
		}

		client := config.Client(ctx, token)
		service, err = gmail.NewService(ctx, option.WithHTTPClient(client))
		if err != nil {
			return nil, fmt.Errorf("failed to create Gmail service with OAuth2: %v", err)
		}
	}

	return service, nil
}

// SendEmail 發送郵件
func (g *GmailService) SendEmail(ctx context.Context, to, subject, body string, attachmentData []byte, attachmentName string) error {
	// 從環境變數讀取發送者郵箱
	v := viper.New()
	v.AutomaticEnv()
	v.SetConfigName(".env")
	v.SetConfigType("env")
	v.AddConfigPath(".")

	if err := v.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config: %v", err)
	}

	fromEmail := v.GetString("GMAIL_FROM")
	if fromEmail == "" {
		return fmt.Errorf("GMAIL_FROM environment variable is not set")
	}

	// 構建郵件內容
	var message strings.Builder

	// 郵件標頭
	message.WriteString(fmt.Sprintf("From: %s\r\n", fromEmail))
	message.WriteString(fmt.Sprintf("To: %s\r\n", to))
	message.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	message.WriteString("MIME-Version: 1.0\r\n")

	if attachmentData != nil && len(attachmentData) > 0 {
		// 有附件的郵件
		boundary := "boundary123456789"
		message.WriteString(fmt.Sprintf("Content-Type: multipart/mixed; boundary=%s\r\n\r\n", boundary))

		// 郵件正文部分
		message.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		message.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		message.WriteString("Content-Transfer-Encoding: quoted-printable\r\n\r\n")
		message.WriteString(body)
		message.WriteString("\r\n\r\n")

		// 附件部分
		message.WriteString(fmt.Sprintf("--%s\r\n", boundary))
		message.WriteString(fmt.Sprintf("Content-Type: application/pdf; name=\"%s\"\r\n", attachmentName))
		message.WriteString("Content-Transfer-Encoding: base64\r\n")
		message.WriteString(fmt.Sprintf("Content-Disposition: attachment; filename=\"%s\"\r\n\r\n", attachmentName))

		// 將附件編碼為 base64
		encodedAttachment := base64.StdEncoding.EncodeToString(attachmentData)
		// 每76個字符換行（RFC 2045標準）
		for i := 0; i < len(encodedAttachment); i += 76 {
			end := i + 76
			if end > len(encodedAttachment) {
				end = len(encodedAttachment)
			}
			message.WriteString(encodedAttachment[i:end])
			message.WriteString("\r\n")
		}

		message.WriteString(fmt.Sprintf("--%s--\r\n", boundary))
	} else {
		// 純文字郵件
		message.WriteString("Content-Type: text/html; charset=UTF-8\r\n")
		message.WriteString("Content-Transfer-Encoding: quoted-printable\r\n\r\n")
		message.WriteString(body)
	}

	// 創建 Gmail 訊息
	gmailMessage := &gmail.Message{
		Raw: base64.URLEncoding.EncodeToString([]byte(message.String())),
	}

	// 發送郵件
	_, err := g.service.Users.Messages.Send("me", gmailMessage).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("failed to send email via Gmail API: %v", err)
	}

	fmt.Printf("Email sent successfully via Gmail API to: %s\n", to)
	return nil
}

// GetAuthURL 獲取 OAuth2 認證 URL
func GetGmailAuthURL() (string, error) {
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return "", fmt.Errorf("failed to read credentials file: %v", err)
	}

	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return "", fmt.Errorf("failed to parse credentials: %v", err)
	}

	authURL := config.AuthCodeURL("state-token", oauth2.AccessTypeOffline)
	return authURL, nil
}

// ExchangeCodeForToken 交換授權碼獲取 token
func ExchangeCodeForToken(ctx context.Context, code string) (*oauth2.Token, error) {
	credentialsFile := "client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json"

	credentialsData, err := os.ReadFile(credentialsFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read credentials file: %v", err)
	}

	config, err := google.ConfigFromJSON(credentialsData, gmail.GmailSendScope)
	if err != nil {
		return nil, fmt.Errorf("failed to parse credentials: %v", err)
	}

	token, err := config.Exchange(ctx, code)
	if err != nil {
		return nil, fmt.Errorf("failed to exchange code for token: %v", err)
	}

	return token, nil
}
