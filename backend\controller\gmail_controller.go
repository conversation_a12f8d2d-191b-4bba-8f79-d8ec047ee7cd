package controller

import (
	"context"
	"cx/service"
	"cx/utils"
	"net/http"

	"github.com/gin-gonic/gin"
)

type GmailController struct {
}

func NewGmailController(r *gin.RouterGroup, tokenAuthMiddleware gin.HandlerFunc) {
	gmailController := GmailController{}

	v1 := r.Group("/v1/gmail")
	{
		// OAuth2 授權相關 (不需要認證)
		v1.GET("/auth-url", gmailController.GetAuthURL)
		v1.POST("/exchange-token", gmailController.ExchangeToken)

		// 需要認證的路由
		v1.Use(tokenAuthMiddleware)
		v1.GET("/status", gmailController.CheckAuthStatus)
		v1.POST("/test-email", gmailController.TestEmail)
	}
}

// GetAuthURL 獲取 Gmail OAuth2 授權 URL
func (ctr *GmailController) GetAuthURL(c *gin.Context) {
	authURL, err := service.GetGmailAuthURL()
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to get Gmail auth URL")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"auth_url": authURL,
		"message":  "Please visit the auth_url to authorize Gmail access",
	})
}

// ExchangeToken 交換授權碼獲取 token
func (ctr *GmailController) ExchangeToken(c *gin.Context) {
	var request struct {
		Code string `json:"code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	ctx := context.Background()
	token, err := service.ExchangeCodeForToken(ctx, request.Code)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to exchange code for token")
		return
	}

	// 保存 token (這裡應該保存到資料庫或安全存儲)
	// 為了簡化，我們暫時只返回成功訊息
	_ = token

	utils.HandleSuccess(c, gin.H{
		"message": "Gmail OAuth2 token obtained and saved successfully",
		"expires": token.Expiry.Format("2006-01-02 15:04:05"),
	})
}

// CheckAuthStatus 檢查 Gmail 授權狀態
func (ctr *GmailController) CheckAuthStatus(c *gin.Context) {
	ctx := context.Background()

	// 嘗試創建 Gmail 服務來檢查授權狀態
	_, err := service.NewGmailService(ctx)
	if err != nil {
		utils.HandleError(c, http.StatusUnauthorized, err, "Gmail not authorized")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"authorized": true,
		"message":    "Gmail is properly authorized",
	})
}

// TestEmail 測試發送郵件
func (ctr *GmailController) TestEmail(c *gin.Context) {
	var request struct {
		To      string `json:"to" binding:"required,email"`
		Subject string `json:"subject" binding:"required"`
		Body    string `json:"body" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		utils.HandleError(c, http.StatusBadRequest, err, "Invalid request body")
		return
	}

	ctx := context.Background()
	gmailService, err := service.NewGmailService(ctx)
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to create Gmail service")
		return
	}

	err = gmailService.SendEmail(ctx, request.To, request.Subject, request.Body, nil, "")
	if err != nil {
		utils.HandleError(c, http.StatusInternalServerError, err, "Failed to send test email")
		return
	}

	utils.HandleSuccess(c, gin.H{
		"message": "Test email sent successfully",
		"to":      request.To,
	})
}
