import{E as x,at as w,au as h,aR as D,c as g,S,J as i,h as y,T as p,G as Q,I as C,Z as B,K as A,d as k,u as P,r as T,o as V,k as F,f as l,b as a,y as b,i as e,Q as I,C as $,m as f,aS as v,z as E}from"./index.a0917d6f.js";import{d as Y,Q as j}from"./QDate.e32febd2.js";import{Q as z}from"./QPopupProxy.68ef9752.js";import{Q as U}from"./QPage.30a4ddee.js";import{C as K}from"./ClosePopup.b8d8b91c.js";import{u as M}from"./pageInfo.9ba74675.js";import{_ as N}from"./plugin-vue_export-helper.21dcd24c.js";import"./format.054b8074.js";import"./QMenu.5058797d.js";import"./selection.6b4c5528.js";var q=x({name:"QTimelineEntry",props:{heading:Boolean,tag:{type:String,default:"h3"},side:{type:String,default:"right",validator:t=>["left","right"].includes(t)},icon:String,avatar:String,color:String,title:String,subtitle:String,body:String},setup(t,{slots:n}){const u=w(D,h);if(u===h)return console.error("QTimelineEntry needs to be child of QTimeline"),h;const o=g(()=>`q-timeline__entry q-timeline__entry--${t.side}`+(t.icon!==void 0||t.avatar!==void 0?" q-timeline__entry--icon":"")),r=g(()=>`q-timeline__dot text-${t.color||u.color}`),_=g(()=>u.layout==="comfortable"&&u.side==="left");return()=>{const m=S(n.default,[]);if(t.body!==void 0&&m.unshift(t.body),t.heading===!0){const c=[i("div"),i("div"),i(t.tag,{class:"q-timeline__heading-title"},m)];return i("div",{class:"q-timeline__heading"},_.value===!0?c.reverse():c)}let d;t.icon!==void 0?d=[i(y,{class:"row items-center justify-center",name:t.icon})]:t.avatar!==void 0&&(d=[i("img",{class:"q-timeline__dot-img",src:t.avatar})]);const s=[i("div",{class:"q-timeline__subtitle"},[i("span",{},p(n.subtitle,[t.subtitle]))]),i("div",{class:r.value},d),i("div",{class:"q-timeline__content"},[i("h6",{class:"q-timeline__title"},p(n.title,[t.title]))].concat(m))];return i("li",{class:o.value},_.value===!0?s.reverse():s)}}}),R=x({name:"QTimeline",props:{...Q,color:{type:String,default:"primary"},side:{type:String,default:"right",validator:t=>["left","right"].includes(t)},layout:{type:String,default:"dense",validator:t=>["dense","comfortable","loose"].includes(t)}},setup(t,{slots:n}){const u=A(),o=C(t,u.proxy.$q);B(D,t);const r=g(()=>`q-timeline q-timeline--${t.layout} q-timeline--${t.layout}--${t.side}`+(o.value===!0?" q-timeline--dark":""));return()=>i("ul",{class:r.value},p(n.default))}});const G={class:"row items-center"},J={class:"col"},L={class:"row items-center justify-end"},O={class:"col full-height text-right"},Z={class:"row"},H={class:"row"},W={class:"row q-mb-lg text-center"},X={class:"col"},ee={class:"d-block"},te={class:"col"},se={class:"d-block"},ae={class:"row"},le=k({__name:"AttendancePage",setup(t){const{t:n}=P(),{setPageTitle:u}=M();u(n("attendance.label"));const o=T(""),r=Y.formatDate(new Date,"YYYY/MM/DD");o.value=r;const _={days:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],daysShort:["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],months:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"],monthsShort:["1\u6708","2\u6708","3\u6708","4\u6708","5\u6708","6\u6708","7\u6708","8\u6708","9\u6708","10\u6708","11\u6708","12\u6708"]};function m(d){return d<=r}return(d,s)=>(V(),F(U,null,{default:l(()=>[a(E,{bordered:"",class:"card"},{default:l(()=>[a(b,{class:"q-px-lg"},{default:l(()=>[s[2]||(s[2]=e("div",{class:"row q-mb-sm"},[e("div",{class:"col text-h6"},"\u738B\u66C9\u660E"),e("div",{class:"col text-right"},"\u54E1\u5DE5\u7DE8\u865F\uFF1A12345678")],-1)),e("div",G,[e("div",J,[a(I,{modelValue:o.value,"onUpdate:modelValue":s[1]||(s[1]=c=>o.value=c),mask:"date"},{append:l(()=>[a(y,{name:"event",class:"cursor-pointer"},{default:l(()=>[a(z,{cover:"","transition-show":"scale","transition-hide":"scale"},{default:l(()=>[a(j,{modelValue:o.value,"onUpdate:modelValue":s[0]||(s[0]=c=>o.value=c),locale:_,options:m},{default:l(()=>[e("div",L,[$(a(f,{label:"Close",color:"primary",flat:""},null,512),[[K]])])]),_:1},8,["modelValue"])]),_:1})]),_:1})]),_:1},8,["modelValue"])]),e("div",O,[a(f,{flat:"",color:"primary",label:"\u524D\u5F80\u67E5\u8A62\u7D00\u9304",to:"/punch-record","icon-right":"arrow_forward"})])])]),_:1}),a(v),a(b,{class:"q-px-lg info"},{default:l(()=>[e("div",Z,[s[3]||(s[3]=e("div",{class:"col"},[e("div",{class:"label"},"\u51FA\u52E4"),e("div",{class:"text-subtitle1"},"20 \u5929")],-1)),a(v,{vertical:""}),s[4]||(s[4]=e("div",{class:"col"},[e("div",{class:"label"},"\u52A0\u73ED"),e("div",{class:"text-subtitle1"},"48 \u5C0F\u6642")],-1))])]),_:1}),a(v),a(b,{class:"q-px-lg info"},{default:l(()=>[e("div",H,[s[5]||(s[5]=e("div",{class:"col"},[e("div",{class:"label"},"\u9072\u5230"),e("div",{class:"text-subtitle1"},"2 \u6B21")],-1)),a(v,{vertical:""}),s[6]||(s[6]=e("div",{class:"col"},[e("div",{class:"label"},"\u65E9\u9000"),e("div",{class:"text-subtitle1"},"1 \u6B21")],-1)),a(v,{vertical:""}),s[7]||(s[7]=e("div",{class:"col"},[e("div",{class:"label"},"\u672A\u6253\u5361"),e("div",{class:"text-subtitle1"},"1 \u6B21")],-1))])]),_:1})]),_:1}),a(E,{class:"q-pa-md q-mt-lg"},{default:l(()=>[e("div",W,[e("div",X,[a(f,{round:"",class:"punch",color:"green"},{default:l(()=>[e("div",ee,[a(y,{size:"xl",name:"arrow_upward"}),s[8]||(s[8]=e("div",{class:"text-h6"},"\u4E0A\u73ED",-1))])]),_:1})]),e("div",te,[a(f,{round:"",class:"punch",color:"red"},{default:l(()=>[e("div",se,[a(y,{size:"xl",name:"arrow_downward"}),s[9]||(s[9]=e("div",{class:"text-h6"},"\u4E0B\u73ED",-1))])]),_:1})])]),e("div",ae,[a(R,{class:"q-mb-none"},{default:l(()=>[a(q,{title:"\u4E0A\u73ED\u6253\u5361\u6642\u959308:00"}),a(q,{title:"\u5C1A\u672A\u6253\u4E0B\u73ED\u5361"})]),_:1})])]),_:1})]),_:1}))}});var fe=N(le,[["__scopeId","data-v-68cfc108"]]);export{fe as default};
