# Gmail API 設置指南

## 概述

本專案已將 Xero 發票郵件發送功能從 Xero API 改為使用 Google Gmail API，以避免 Xero 的郵件發送限制。

## 快速解決 OAuth2 認證問題

### 錯誤訊息
如果您看到以下錯誤：
```
Error 401: Request had invalid authentication credentials. Expected OAuth 2 access token
```

這表示需要完成 Gmail OAuth2 授權。

### 解決步驟

#### 方法 1: 使用 API 端點 (推薦)

1. **啟動後端服務**
2. **獲取授權 URL**：
   ```bash
   curl http://localhost:8080/au-pos/api/v1/gmail/auth-url
   ```

3. **在瀏覽器中打開返回的 auth_url**
4. **完成 Google 授權**，獲得授權碼
5. **交換授權碼獲取 token**：
   ```bash
   curl -X POST http://localhost:8080/au-pos/api/v1/gmail/exchange-token \
     -H "Content-Type: application/json" \
     -d '{"code": "YOUR_AUTHORIZATION_CODE"}'
   ```

#### 方法 2: 使用命令行工具

1. **獲取授權 URL**：
   ```bash
   cd backend
   go run cmd/gmail_auth/main.go auth
   ```

2. **在瀏覽器中完成授權**，獲得授權碼

3. **使用授權碼獲取 token**：
   ```bash
   go run cmd/gmail_auth/main.go token YOUR_AUTHORIZATION_CODE
   ```

#### 方法 3: 使用內建授權服務器

```bash
cd backend
go run cmd/gmail_auth/main.go server
```

然後在瀏覽器中訪問 `http://localhost:8081/auth`

## 設置步驟

### 1. Google Cloud Console 設置

1. 前往 [Google Cloud Console](https://console.cloud.google.com/)
2. 選擇或創建一個專案
3. 啟用 Gmail API：
   - 在左側導航中選擇 "APIs & Services" > "Library"
   - 搜索 "Gmail API" 並啟用它

### 2. 創建 OAuth2 憑證

1. 在 Google Cloud Console 中，前往 "APIs & Services" > "Credentials"
2. 點擊 "Create Credentials" > "OAuth client ID"
3. 選擇 "Web application"
4. 設置授權重定向 URI（例如：`http://localhost:8080/auth/gmail/callback`）
5. 下載憑證 JSON 文件
6. 將文件重命名為 `client_secret_212236466011-s5f2mj91k6tf9gmgsr6g8vo1e1e8ag7i.apps.googleusercontent.com.json`
7. 將文件放在 `backend/` 目錄下

### 3. 環境變數設置

在 `.env` 文件中添加：

```env
GMAIL_FROM=<EMAIL>
```

### 4. OAuth2 授權流程

由於 Gmail API 需要 OAuth2 授權，您需要實現以下流程：

#### 4.1 獲取授權 URL

```go
authURL, err := GetGmailAuthURL()
if err != nil {
    log.Fatal(err)
}
fmt.Printf("請訪問以下 URL 進行授權: %s\n", authURL)
```

#### 4.2 交換授權碼獲取 Token

用戶授權後會獲得一個授權碼，使用此碼獲取 access token：

```go
token, err := ExchangeCodeForToken(ctx, authorizationCode)
if err != nil {
    log.Fatal(err)
}
// 將 token 保存到資料庫或安全存儲中
```

### 5. 使用方式

修改後的 `SendInvoiceEmail` 函數會自動：

1. 獲取發票資訊
2. 生成發票 PDF（如果可用）
3. 創建包含發票詳情的 HTML 郵件
4. 使用 Gmail API 發送郵件（包含 PDF 附件）

```go
err := xeroService.SendInvoiceEmail(ctx, invoiceID, customerEmail)
if err != nil {
    log.Printf("發送郵件失敗: %v", err)
}
```

## 重要注意事項

### 1. Token 管理

- Access Token 通常在 1 小時後過期
- Refresh Token 可用於獲取新的 Access Token
- 建議將 Token 安全地存儲在資料庫中
- 實現 Token 自動刷新機制

### 2. 安全性

- 憑證文件包含敏感資訊，不應提交到版本控制
- 在生產環境中使用環境變數或安全的密鑰管理服務
- 定期輪換 OAuth2 憑證

### 3. 限制

- Gmail API 有每日配額限制
- 每個用戶每秒請求數限制
- 建議實現重試機制和錯誤處理

## 故障排除

### 常見錯誤

1. **憑證文件未找到**
   - 確保憑證文件在正確位置
   - 檢查文件名是否正確

2. **授權失敗**
   - 確保重定向 URI 配置正確
   - 檢查 OAuth2 範圍是否包含 Gmail 發送權限

3. **Token 過期**
   - 實現 Token 刷新機制
   - 檢查 Refresh Token 是否有效

### 調試

啟用詳細日誌來調試問題：

```go
fmt.Printf("Gmail service created successfully\n")
fmt.Printf("Sending email to: %s\n", email)
fmt.Printf("Subject: %s\n", subject)
```

## 生產環境部署

1. 使用環境變數管理敏感配置
2. 實現 Token 存儲和刷新機制
3. 設置監控和日誌記錄
4. 配置錯誤處理和重試邏輯
5. 定期備份 OAuth2 Token

## 相關文件

- [Gmail API 文檔](https://developers.google.com/gmail/api)
- [Google OAuth2 文檔](https://developers.google.com/identity/protocols/oauth2)
- [Go Gmail API 客戶端](https://pkg.go.dev/google.golang.org/api/gmail/v1)
