import{bh as t}from"./index.a0917d6f.js";const a={listCategories:()=>t.get("v1/product-categories"),get:e=>t.get(`v1/product-categories/${e}`),create:e=>t.post("v1/product-categories",e),update:e=>t.put(`v1/product-categories/${e.id}`,e),delete:e=>t.delete(`v1/product-categories/${e}`),uploadImage:(e,o)=>t.uploadFile(`v1/product-categories/${e}/images`,o),deleteImage:(e,o)=>t.delete(`v1/product-categories/${e}/images/${o}`)};export{a as P};
