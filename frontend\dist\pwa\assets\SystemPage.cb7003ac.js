import{bh as u,d as f,u as y,r as p,s as b,o as x,k as w,f as d,b as a,g as h,y as q,i as s,p as Q,h as V,aS as S,Q as c,q as v,aH as k,m as C,z,a$ as B}from"./index.a0917d6f.js";import{Q as N}from"./QForm.de8ad275.js";import{Q as P}from"./QPage.30a4ddee.js";const _={get:()=>u.get("v1/systems"),update:r=>u.put("v1/systems",r)},A={class:"row q-mb-md"},I={class:"col-12 text-h6 text-bold text-black"},M={class:"row items-center"},G={href:"https://myaccount.google.com/apppasswords",target:"_blank",class:"self-center q-ml-sm"},T={class:"row q-mb-md"},U={class:"col-12 col-md-10"},D={class:"row q-mb-md"},F={class:"col-12 col-md-10"},$=f({__name:"SystemPage",setup(r){const{t:i}=y(),t=p({gmail:"",gmail_app_password:""}),l=p(!1),m=async()=>{try{l.value=!0;const n=await _.get();t.value=n.result}finally{l.value=!1}},g=async()=>{try{l.value=!0,await _.update(t.value),B.create({type:"positive",message:i("success"),position:"top"})}finally{l.value=!1,m()}};return b(()=>{m()}),(n,e)=>(x(),w(P,{class:"q-pt-md"},{default:d(()=>[a(z,{flat:"",square:"",bordered:"",class:"q-mx-auto",style:{width:"800px","max-width":"100%"}},{default:d(()=>[a(N,{onSubmit:h(g,["prevent"]),greedy:""},{default:d(()=>[a(q,null,{default:d(()=>[s("div",A,[s("div",I,[s("div",M,[e[2]||(e[2]=Q(" Gmail STMP ")),s("a",G,[a(V,{name:"help",size:"sm",color:"primary"})])]),a(S,{color:"black",size:"2px",class:"q-mb-sm"})])]),s("div",T,[e[3]||(e[3]=s("div",{class:"col-12 col-md-2 text-subtitle1 text-md-center"}," Gmail ",-1)),s("div",U,[a(c,{type:"email",modelValue:t.value.gmail,"onUpdate:modelValue":e[0]||(e[0]=o=>t.value.gmail=o),outlined:"",dense:"","hide-bottom-space":""},null,8,["modelValue"])])]),s("div",D,[e[4]||(e[4]=s("div",{class:"col-12 col-md-2 text-subtitle1 text-md-center"}," App Password ",-1)),s("div",F,[a(c,{modelValue:t.value.gmail_app_password,"onUpdate:modelValue":e[1]||(e[1]=o=>t.value.gmail_app_password=o),outlined:"",dense:"","hide-bottom-space":"",rules:[o=>!!o||!t.value.gmail||v(i)("error.required")]},null,8,["modelValue","rules"])])])]),_:1}),a(k,{align:"right",class:"q-px-lg q-py-md"},{default:d(()=>[a(C,{type:"submit",label:v(i)("save"),color:"positive",size:"md",loading:l.value},null,8,["label","loading"])]),_:1})]),_:1})]),_:1})]),_:1}))}});export{$ as default};
